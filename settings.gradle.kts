import org.gradle.kotlin.dsl.maven

pluginManagement {
  repositories {
    google {
      content {
        includeGroupByRegex("com\\.android.*")
        includeGroupByRegex("com\\.google.*")
        includeGroupByRegex("androidx.*")
      }
    }
    mavenCentral()
    gradlePluginPortal()
  }
}
dependencyResolutionManagement {
  repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
  repositories {
    google()
    mavenCentral()
    maven { url = uri("https://artifact.bytedance.com/repository/Volcengine/") }
    maven { url = uri("https://artifact.byteplus.com/repository/public/") }
  }
}

rootProject.name = "Shorts"
include(":app")
