# Shorts功能测试指南

## 功能概述

本项目实现了类似TikTok的短剧Feed功能，支持：
- 垂直滑动浏览短剧
- 当前页面自动播放视频
- Snap-to-item效果
- 点赞和分享交互

## 测试前准备

### 1. 网络要求
- 确保设备连接到稳定的网络
- 如果在不支持的地区，需要使用VPN连接到支持的地区

### 2. 支持的地区
根据Dramaverse SDK文档，支持的地区包括：
- 中国台湾、泰国、越南、马来西亚、印度尼西亚、菲律宾、新加坡、柬埔寨
- 日本、韩国、巴西、澳大利亚、新西兰
- 阿根廷、智利、哥伦比亚、厄瓜多尔、秘鲁、乌拉圭、墨西哥
- 沙特阿拉伯、阿联酋、土耳其等中东地区
- 其他支持地区请参考SDK文档

### 3. 设备要求
- Android API 26+
- 支持的CPU架构：arm64-v8a
- 足够的内存来播放视频

## 测试步骤

### 1. 启动应用
1. 编译并安装应用到设备
2. 启动应用
3. 等待SDK初始化完成（查看日志确认）

### 2. 进入Shorts页面
1. 在底部导航栏点击"Shorts"标签
2. 应用会自动加载短剧列表
3. 如果看到加载指示器，等待数据加载完成

### 3. 测试垂直滑动
1. **初始状态**：第一个视频应该自动开始播放
2. **向上滑动**：滑动到下一个视频
   - 当前视频应该暂停
   - 新视频应该自动开始播放
   - 滑动应该有snap效果，精确停在视频上
3. **向下滑动**：滑动回到上一个视频
   - 测试双向滑动是否正常

### 4. 测试播放功能
1. **自动播放**：当前页面的视频应该自动播放
2. **暂停/恢复**：滑动离开后再回来，视频应该重新开始播放
3. **封面显示**：非当前页面应该显示视频封面和播放按钮

### 5. 测试交互功能
1. **点赞按钮**：点击右侧的点赞按钮
   - 检查日志是否有相应的输出
   - UI状态是否正确更新
2. **分享按钮**：点击分享按钮
   - 检查日志是否有相应的输出

### 6. 测试分页加载
1. 持续向上滑动浏览视频
2. 当接近列表末尾时，应该自动加载更多内容
3. 检查底部是否显示加载指示器

### 7. 测试错误处理
1. **网络错误**：断开网络连接，观察错误处理
2. **重试功能**：在错误状态下点击重试按钮
3. **地区限制**：如果在不支持的地区，应该显示相应错误信息

## 预期行为

### 正常情况
- ✅ 视频列表正常加载
- ✅ 当前页面视频自动播放
- ✅ 滑动切换流畅，有snap效果
- ✅ 非当前页面显示封面图
- ✅ 分页加载正常工作
- ✅ 点赞/分享按钮响应正常

### 异常情况
- ❌ 如果SDK初始化失败，会显示错误信息
- ❌ 如果网络连接问题，会显示加载失败
- ❌ 如果在不支持地区，会显示地区限制错误

## 调试信息

### 查看日志
使用以下命令查看应用日志：
```bash
adb logcat | grep -E "(ShortsScreen|InlineVideoPlayer|ShortsUiModel|PSSDK)"
```

### 关键日志标签
- `ShortsScreen`: 主屏幕相关日志
- `InlineVideoPlayer`: 视频播放器相关日志
- `ShortsUiModel`: 数据加载相关日志
- `PSSDK`: Dramaverse SDK相关日志

### 常见问题排查

1. **视频不播放**
   - 检查网络连接
   - 确认是否在支持的地区
   - 查看PSSDK相关错误日志

2. **滑动不流畅**
   - 检查设备性能
   - 查看内存使用情况
   - 确认是否有其他应用占用资源

3. **数据加载失败**
   - 检查网络连接
   - 查看API请求相关日志
   - 确认SDK配置是否正确

## 性能测试

### 内存使用
- 监控应用内存使用情况
- 检查是否有内存泄漏
- 观察长时间使用后的性能表现

### 网络使用
- 监控网络请求频率
- 检查视频预加载策略
- 观察数据使用量

### 电池消耗
- 测试长时间使用的电池消耗
- 对比不同播放模式的耗电情况

## 反馈收集

测试过程中请记录：
1. 设备信息（型号、Android版本）
2. 网络环境（WiFi/移动网络、地区）
3. 遇到的问题和错误信息
4. 性能表现（流畅度、加载速度）
5. 用户体验反馈

## 后续优化建议

基于测试结果，可能需要优化的方面：
1. 视频预加载策略
2. 内存管理优化
3. 网络错误处理改进
4. UI交互体验提升
5. 性能优化
