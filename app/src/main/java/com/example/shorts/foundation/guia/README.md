# Navigation Components Guide

This document provides an overview of the navigation components available in the `com.example.pdf.guia` package and how to use them in your PDF application.

## Overview

These navigation components are built on top of the Guia navigation library and provide a streamlined approach for implementing navigation in your application. The package includes components for screens, bottom sheets, dialogs, and transition animations.

## Components

### GlobalNavigator

`GlobalNavigator` provides a central point for navigation actions across the application.

**Key Features:**
- Singleton object that can be accessed from anywhere in the app
- Allows for both immediate and queued navigation actions
- Thread-safe navigation through coroutine channels

**Usage:**

1. Register the navigator in your main composable:
<pre><code>@Composable
fun MyApp() {
    val navigator = rememberNavigator()
    val scope = rememberCoroutineScope()
    
    GlobalNavigator.Register(navigator, scope)
    
    // Your app content
}
</code></pre>

2. Navigate from anywhere in your code:
<pre><code>// For regular navigation actions
GlobalNavigator.tryTransaction {
    push(MyScreenNode())
}

// For navigation actions that need to be suspending
lifecycleScope.launch {
    GlobalNavigator.transaction {
        push(MyScreenNode())
    }
}
</code></pre>

### ScreenNode

`ScreenNode` is the base class for defining screen destinations in your app.

**Usage:**

1. Create a new screen by extending `ScreenNode`:
<pre><code>class HomeScreen : ScreenNode("home_screen") {
    @Composable
    override fun Content(navigator: Navigator) {
        // Your screen content here
        
        Button(onClick = { navigator.push(DetailsScreen()) }) {
            Text("Go to details")
        }
    }
}
</code></pre>

2. Navigate to this screen:
<pre><code>navigator.push(HomeScreen())
</code></pre>

### BottomSheetNode

`BottomSheetNode` allows you to create bottom sheets with custom content and behavior.

**Usage:**

1. Create a bottom sheet by extending `BottomSheetNode`:
<pre><code>class OptionsBottomSheet : BottomSheetNode(
    tag = "options_bottom_sheet",
    bottomSheetOptions = BottomSheet.BottomSheetOptions(
        isDismissible = true,
        // Other options
    )
) {
    @Composable
    override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
        // Bottom sheet content
        
        Button(onClick = { navigator.pop() }) {
            Text("Close")
        }
    }
}
</code></pre>

2. Show the bottom sheet:
<pre><code>navigator.push(OptionsBottomSheet())
</code></pre>

### DialogNode

`DialogNode` allows you to create custom dialog components.

**Usage:**

1. Create a dialog by extending `DialogNode`:
<pre><code>class ConfirmationDialog(
    private val onConfirm: () -> Unit
) : DialogNode(
    tag = "confirmation_dialog",
    dialogOptions = Dialog.DialogOptions(
        isDismissible = true,
        // Other options
    )
) {
    @Composable
    override fun Content(navigator: Navigator, dialog: Dialog?) {
        // Dialog content
        
        Row {
            Button(onClick = { 
                onConfirm()
                navigator.pop() 
            }) {
                Text("Confirm")
            }
            
            Button(onClick = { navigator.pop() }) {
                Text("Cancel")
            }
        }
    }
}
</code></pre>

2. Show the dialog:
<pre><code>navigator.push(ConfirmationDialog { 
    // Handle confirmation 
})
</code></pre>

### Transitions

The package provides two transition types:

1. **HorizontalSlideTransitions**: Slide animations for horizontal navigation
   - `HorizontalSlideTransitions.Default`: Standard horizontal slide
   - Custom durations can be configured using `buildSlideTransition(animationDuration)`

2. **FadeTransitions**: Fade animations
   - `FadeTransitions.Default`: Standard fade
   - `FadeTransitions.FullFade`: Complete fade from transparent
   - `FadeTransitions.SemiFade`: Fade with semi-transparent background
   - Custom fade transitions can be created using `createFadeTransition()`

**Usage:**
<pre><code>// Apply transitions when pushing a screen
navigator.push(DetailScreen(), transition = HorizontalSlideTransitions.Default)

// Or use fade transitions
navigator.push(OverlayScreen(), transition = FadeTransitions.SemiFade)
</code></pre>

### NavigatorExt

Extension properties for the Navigator:

- `previousKey`: Provides access to the previous navigation key in the backstack

**Usage:**
<pre><code>val previousScreen = navigator.previousKey
if (previousScreen is HomeScreen) {
    // We came from home screen
}
</code></pre>

## Integration Example

Here's a comprehensive example showing how these components work together:
<pre><code>// Define your screens
class HomeScreen : ScreenNode("home") {
    @Composable
    override fun Content(navigator: Navigator) {
        Column {
            Text("Home Screen")
            Button(onClick = { 
                navigator.push(DetailScreen(), transition = HorizontalSlideTransitions.Default) 
            }) {
                Text("Go to Detail")
            }
            Button(onClick = { 
                navigator.push(SettingsBottomSheet()) 
            }) {
                Text("Show Settings")
            }
        }
    }
}

// Set up the navigator in your app
@Composable
fun MyApp() {
    val navigator = rememberNavigator(HomeScreen())
    val scope = rememberCoroutineScope()
    
    GlobalNavigator.Register(navigator, scope)
    
    GuiaContainer(navigator = navigator) {
        // Your app content
    }
}
</code></pre>

## Best Practices

1. Use `GlobalNavigator` for app-wide navigation that needs to be triggered from non-composable contexts
2. For local navigation within a feature, use the provided `navigator` parameter in each component
3. Always provide meaningful tags for your navigation nodes to aid in debugging
4. Consider using transitions to provide visual cues about navigation direction and hierarchy

For more advanced usage, refer to the Guia library documentation.