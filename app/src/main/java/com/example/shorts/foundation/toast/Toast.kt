package com.example.shorts.foundation.toast

import android.widget.Toast
import com.example.shorts.initialize.appContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch

fun showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
  GlobalScope.launch(Dispatchers.Main) {
    Toast.makeText(appContext, message, duration).show()
  }
}


@Suppress("NOTHING_TO_INLINE")
inline fun showToast(
  duration: Int = Toast.LENGTH_SHORT,
  noinline message: () -> String
) {
  showToast(message = message(), duration = duration)
}
