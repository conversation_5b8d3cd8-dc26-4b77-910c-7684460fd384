package com.example.shorts.foundation.guia

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.localDialog
import com.roudikk.guia.extensions.requireLocalNavigator


abstract class DialogNode(
  private val tag: String,
  private val dialogOptions: Dialog.DialogOptions = Dialog.DialogOptions()
) : NavigationKey.WithNode<Dialog> {

  override fun tag() = tag

  override fun navigationNode() = Dialog {
    val dialog = localDialog()
    LaunchedEffect(dialog) {
      dialog?.dialogOptions = dialogOptions
    }

    Content(
      navigator = requireLocalNavigator(),
      dialog = dialog
    )
  }

  @Composable
  abstract fun Content(
    navigator: Navigator,
    dialog: Dialog?,
  )
}