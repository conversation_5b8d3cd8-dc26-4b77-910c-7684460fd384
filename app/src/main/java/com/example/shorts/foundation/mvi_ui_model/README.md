# UiModel Documentation

`UiModel` is a base class that combines Android's ViewModel architecture component with Orbit MVI pattern to manage UI state in a predictable way.

## Overview

`UiModel` extends Android's `ViewModel` and implements Orbit's `ContainerHost` interface to provide a structured approach to UI state management using the MVI (Model-View-Intent) pattern.

## Features

- Built on Android's ViewModel architecture component
- Integrates with Orbit MVI for predictable state management
- Simplifies state and side-effect handling
- Compatible with Koin dependency injection

## Usage

### Creating a UiModel

To create your own UiModel, extend the `UiModel` abstract class with your state and side-effect types:

```kotlin
@KoinViewModel
class MyUiModel : UiModel<MyState, MySideEffect>(MyState()) {
    
    // Define your intents as functions that modify state
    // Use 'on' prefix for intent functions
    fun onUpdateSomething(data: String) = intent {
        reduce {
            state.copy(someData = data)
        }
    }
    
    // Handle side effects
    fun onPerformAction() = intent {
        // Do something
        postSideEffect(MySideEffect.ActionCompleted)
    }
}

// Define your state data class
data class MyState(
    val someData: String = "",
    val isLoading: Boolean = false
)

// Define your side effects sealed class
sealed class MySideEffect {
    data object ActionCompleted : MySideEffect()
    data class Error(val message: String) : MySideEffect()
}
```

### Using UiModel in Composables

Use the provided `koinUiModel()` composable function to inject your UiModel:

```kotlin
@Composable
fun MyScreen() {
    val myUiModel: MyUiModel = koinUiModel()
    val state by myUiModel.collectAsState()
    
    // Observe side effects
    val context = LocalContext.current
    myUiModel.collectSideEffect { sideEffect ->
      when (sideEffect) {
        is MySideEffect.ActionCompleted -> {
          // Handle side effect
          Toast.makeText(context, "Action completed", Toast.LENGTH_SHORT).show()
        }
        is MySideEffect.Error -> {
          // Handle error
          Toast.makeText(context, sideEffect.message, Toast.LENGTH_LONG).show()
        }
      }
    }
    
    // Your UI using state
    Column {
        Text(text = state.someData)
        
        Button(
            onClick = { myUiModel.onUpdateSomething("New Data") },
            enabled = !state.isLoading
        ) {
            Text("Update Data")
        }
    }
}
```

## Setup in Koin

When using the `@KoinViewModel` annotation, Koin's annotation processor automatically generates the module, so there's no need to manually create modules for UiModels:

```kotlin
// No need for this anymore:
// val appModule = module {
//     viewModel { MyUiModel(initialState = MyState()) }
// }
```

Just add the `@KoinViewModel` annotation to your UiModel classes and they will be automatically available through Koin dependency injection.

## Key Components

- `UiModel<STATE, SIDE_EFFECT>`: Base abstract class that handles state and side effects
- `container`: Orbit container that manages state and side effects
- `uiModelScope`: Convenient access to the ViewModel's coroutine scope
- `koinUiModel()`: Composable function to obtain a UiModel instance using Koin dependency injection

## Advanced Usage

### Handling Loading States

```kotlin
fun onLoadData() = intent {
    reduce {
        state.copy(isLoading = true)
    }
    
    try {
        val result = repository.fetchData()
        reduce {
            state.copy(
                data = result,
                isLoading = false
            )
        }
    } catch (e: Exception) {
        reduce {
            state.copy(isLoading = false)
        }
        postSideEffect(MySideEffect.Error(e.message ?: "Unknown error"))
    }
}
```

### Sharing UiModel Between Multiple Screens

Use the `koinUiModel()` function with a specific `uiModelStoreOwner` to share the same UiModel instance:

```kotlin
@Composable
fun SharedScreen(sharedOwner: UiModelStoreOwner) {
    val sharedUiModel: SharedUiModel = koinUiModel(uiModelStoreOwner = sharedOwner)
    // Use the shared UiModel
}
```