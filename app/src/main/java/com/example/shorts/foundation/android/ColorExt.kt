package com.example.shorts.foundation.android

import androidx.compose.ui.graphics.Color
import kotlin.math.min

fun Color.brighten(factor: Float = 0.25f): Color {
  val currentRed = red
  val currentGreen = green
  val currentBlue = blue

  val newRed = min(1f, currentRed + (1f - currentRed) * factor)
  val newGreen = min(1f, currentGreen + (1f - currentGreen) * factor)
  val newBlue = min(1f, currentBlue + (1f - currentBlue) * factor)

  return Color(
    red = newRed,
    green = newGreen,
    blue = newBlue,
    alpha = alpha
  )
}