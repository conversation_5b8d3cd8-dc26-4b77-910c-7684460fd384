package com.example.shorts.foundation.in_app_message

import com.tencent.mmkv.MMKV
import org.koin.core.annotation.Single

@Single
class InAppMessageKvStore {
  private val mmkv: MMKV by lazy { MMKV.mmkvWithID("in_app_message") }

  var lastPaypalWithdrawMessageShownTime: Long
    get() = mmkv.decodeLong("last_paypal_withdraw_message_shown_time", 0L)
    set(value) {
      mmkv.encode("last_paypal_withdraw_message_shown_time", value)
    }
}