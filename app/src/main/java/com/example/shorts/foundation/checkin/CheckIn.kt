package com.example.shorts.foundation.checkin

import android.os.Parcelable
import androidx.compose.ui.graphics.vector.ImageVector
import com.example.shorts.foundation.earn.Reward
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal
import java.time.LocalDate

@Parcelize
data class CheckInReward(
  val day: Int,
  val reward: Reward
) : Parcelable

sealed class CheckInResult {
  data class Success(
    val day: Int,
    val reward: CheckInReward,
    val isNewCycle: Boolean = false
  ) : CheckInResult()

  object AlreadyCheckedIn : CheckInResult()
}

sealed class CheckInGridItem {
  data class CheckInDay(
    val day: Int,
    val reward: CheckInReward,
    val isChecked: Boolean,
    val isToday: Boolean
  ) : CheckInGridItem()

  data class ExternalLink(
    val icon: ImageVector,
    val onClick: () -> Unit = {}
  ) : CheckInGridItem()
}

data class CheckInState(
  val consecutiveDays: Int,
  val todayCheckedIn: Boolean,
  val lastCheckInDate: LocalDate?,
  val currentDay: Int
)

val DefaultCheckInRewards = listOf(
  CheckInReward(1, Reward(BigDecimal("100"))),
  CheckInReward(2, Reward(BigDecimal("200"))),
  CheckInReward(3, Reward(BigDecimal("160"))),
  CheckInReward(4, Reward(BigDecimal("100"))),
  CheckInReward(5, Reward(BigDecimal("2000"))),
  CheckInReward(6, Reward(BigDecimal("2000"))),
  CheckInReward(7, Reward(BigDecimal("6000")))
)

interface CheckInRepository {
  fun getCheckInState(): CheckInState
  fun checkIn(): CheckInResult
  fun getCheckInRewards(): List<CheckInReward>
}