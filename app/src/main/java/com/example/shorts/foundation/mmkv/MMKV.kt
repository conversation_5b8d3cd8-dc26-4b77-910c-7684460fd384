package com.example.shorts.foundation.mmkv

import android.os.Parcelable
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.flow.MutableStateFlow
import java.io.ByteArrayOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable
import kotlin.properties.ReadOnlyProperty
import kotlin.reflect.KProperty

/**
 * Interface that marks a class as an MMKV owner, required for property delegation
 */
interface IMMKVOwner {
  val mmkv: MMKV
}

/**
 * Base property delegate for MMKV stored values
 */
abstract class MMKVProperty<V> {
  abstract fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): V
  abstract fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: V)
}

/**
 * MutableStateFlow wrapper for MMKV values
 */
class MMKVFlow<V>(
  private val getMMKVValue: () -> V,
  private val setMMKVValue: (V) -> Unit,
  private val flow: MutableStateFlow<V> = MutableStateFlow(getMMKVValue())
) : MutableStateFlow<V> by flow {
  override var value: V
    get() = getMMKVValue()
    set(value) {
      val origin = flow.value
      flow.value = value
      if (origin != value) {
        setMMKVValue(value)
      }
    }

  override fun compareAndSet(expect: V, update: V): Boolean =
    flow.compareAndSet(expect, update).also { setSuccess ->
      if (setSuccess) setMMKVValue(value)
    }
}

/**
 * Property delegate for MMKV StateFlow values
 */
class MMKVStateFlowProperty<V>(private val mmkvProperty: MMKVProperty<V>) :
  ReadOnlyProperty<IMMKVOwner, MutableStateFlow<V>> {
  private var cache: MutableStateFlow<V>? = null

  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): MutableStateFlow<V> =
    cache ?: MMKVFlow(
      { mmkvProperty.getValue(thisRef, property) },
      { mmkvProperty.setValue(thisRef, property, it) }
    ).also { cache = it }
}

// ======= Base MMKV Property Implementations =======

class BooleanMMKVProperty(
  private val key: String? = null,
  private val defaultValue: Boolean = false
) : MMKVProperty<Boolean>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): Boolean =
    thisRef.mmkv.decodeBool(key ?: property.name, defaultValue)

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: Boolean) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class IntMMKVProperty(
  private val key: String? = null,
  private val defaultValue: Int = 0
) : MMKVProperty<Int>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): Int =
    thisRef.mmkv.decodeInt(key ?: property.name, defaultValue)

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: Int) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class LongMMKVProperty(
  private val key: String? = null,
  private val defaultValue: Long = 0L
) : MMKVProperty<Long>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): Long =
    thisRef.mmkv.decodeLong(key ?: property.name, defaultValue)

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: Long) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class FloatMMKVProperty(
  private val key: String? = null,
  private val defaultValue: Float = 0f
) : MMKVProperty<Float>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): Float =
    thisRef.mmkv.decodeFloat(key ?: property.name, defaultValue)

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: Float) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class DoubleMMKVProperty(
  private val key: String? = null,
  private val defaultValue: Double = 0.0
) : MMKVProperty<Double>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): Double =
    thisRef.mmkv.decodeDouble(key ?: property.name, defaultValue)

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: Double) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class StringMMKVProperty(
  private val key: String? = null,
  private val defaultValue: String = ""
) : MMKVProperty<String>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): String =
    thisRef.mmkv.decodeString(key ?: property.name, defaultValue) ?: defaultValue

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: String) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class StringSetMMKVProperty(
  private val key: String? = null,
  private val defaultValue: Set<String> = emptySet()
) : MMKVProperty<Set<String>>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): Set<String> =
    thisRef.mmkv.decodeStringSet(key ?: property.name, defaultValue) ?: defaultValue

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: Set<String>) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

class ByteArrayMMKVProperty(
  private val key: String? = null,
  private val defaultValue: ByteArray = ByteArray(0)
) : MMKVProperty<ByteArray>() {
  override fun getValue(thisRef: IMMKVOwner, property: KProperty<*>): ByteArray =
    thisRef.mmkv.decodeBytes(key ?: property.name) ?: defaultValue

  override fun setValue(thisRef: IMMKVOwner, property: KProperty<*>, value: ByteArray) {
    thisRef.mmkv.encode(key ?: property.name, value)
  }
}

// ======= Extension functions for IMMKVOwner =======

fun IMMKVOwner.boolean(
  key: String? = null,
  defaultValue: Boolean = false
) = BooleanMMKVProperty(key, defaultValue)

fun IMMKVOwner.int(
  key: String? = null,
  defaultValue: Int = 0
) = IntMMKVProperty(key, defaultValue)

fun IMMKVOwner.long(
  key: String? = null,
  defaultValue: Long = 0L
) = LongMMKVProperty(key, defaultValue)

fun IMMKVOwner.float(
  key: String? = null,
  defaultValue: Float = 0f
) = FloatMMKVProperty(key, defaultValue)

fun IMMKVOwner.double(
  key: String? = null,
  defaultValue: Double = 0.0
) = DoubleMMKVProperty(key, defaultValue)

fun IMMKVOwner.string(
  key: String? = null,
  defaultValue: String = ""
) = StringMMKVProperty(key, defaultValue)

fun IMMKVOwner.stringSet(
  key: String? = null,
  defaultValue: Set<String> = emptySet()
) = StringSetMMKVProperty(key, defaultValue)

fun IMMKVOwner.bytes(
  key: String? = null,
  defaultValue: ByteArray = ByteArray(0)
) = ByteArrayMMKVProperty(key, defaultValue)

// ======= StateFlow Extensions =======

fun <V> MMKVProperty<V>.asStateFlow() = MMKVStateFlowProperty(this)

fun IMMKVOwner.booleanFlow(
  key: String? = null,
  defaultValue: Boolean = false
) = boolean(key, defaultValue).asStateFlow()

fun IMMKVOwner.intFlow(
  key: String? = null,
  defaultValue: Int = 0
) = int(key, defaultValue).asStateFlow()

fun IMMKVOwner.longFlow(
  key: String? = null,
  defaultValue: Long = 0L
) = long(key, defaultValue).asStateFlow()

fun IMMKVOwner.floatFlow(
  key: String? = null,
  defaultValue: Float = 0f
) = float(key, defaultValue).asStateFlow()

fun IMMKVOwner.doubleFlow(
  key: String? = null,
  defaultValue: Double = 0.0
) = double(key, defaultValue).asStateFlow()

fun IMMKVOwner.stringFlow(
  key: String? = null,
  defaultValue: String = ""
) = string(key, defaultValue).asStateFlow()

fun IMMKVOwner.stringSetFlow(
  key: String? = null,
  defaultValue: Set<String> = emptySet()
) = stringSet(key, defaultValue).asStateFlow()

fun IMMKVOwner.bytesFlow(
  key: String? = null,
  defaultValue: ByteArray = ByteArray(0)
) = bytes(key, defaultValue).asStateFlow()

// ======= Direct MMKV Extensions =======

fun MMKV.getStringStateFlow(key: String, defaultValue: String = "") =
  MMKVFlow(
    { decodeString(key) ?: defaultValue },
    { encode(key, it) },
  )

fun MMKV.getBooleanStateFlow(key: String, defaultValue: Boolean = false) =
  MMKVFlow(
    { decodeBool(key, defaultValue) },
    { encode(key, it) },
  )

fun MMKV.getIntStateFlow(key: String, defaultValue: Int = 0) =
  MMKVFlow(
    { decodeInt(key, defaultValue) },
    { encode(key, it) },
  )

fun MMKV.getLongStateFlow(key: String, defaultValue: Long = 0L) =
  MMKVFlow(
    { decodeLong(key, defaultValue) },
    { encode(key, it) },
  )

fun MMKV.getFloatStateFlow(key: String, defaultValue: Float = 0f) =
  MMKVFlow(
    { decodeFloat(key, defaultValue) },
    { encode(key, it) },
  )

fun MMKV.getDoubleStateFlow(key: String, defaultValue: Double = 0.0) =
  MMKVFlow(
    { decodeDouble(key, defaultValue) },
    { encode(key, it) },
  )

fun MMKV.getStringSetStateFlow(key: String, defaultValue: Set<String> = emptySet()) =
  MMKVFlow(
    { decodeStringSet(key) ?: defaultValue },
    { encode(key, it) },
  )

fun MMKV.getBytesStateFlow(key: String, defaultValue: ByteArray = ByteArray(0)) =
  MMKVFlow(
    { decodeBytes(key) ?: defaultValue },
    { encode(key, it) },
  )

/**
 * Helper function for decoding Parcelable objects generically
 */
@Suppress("UNCHECKED_CAST")
inline fun <reified T : Parcelable> MMKV.decodeParcelable(
  key: String,
  defaultValue: T? = null
): T? {
  return decodeParcelable(key, T::class.java) as? T ?: defaultValue
}

/**
 * Helper function for encoding any Parcelable objects
 */
fun MMKV.encodeParcelable(key: String, parcelable: Parcelable?) {
  encode(key, parcelable)
}

/**
 * Helper function for decoding Serializable objects that are also Parcelable
 */
@Suppress("UNCHECKED_CAST")
inline fun <reified T> MMKV.decodeSerializable(
  key: String,
  defaultValue: T? = null
): T? where T : Serializable {
  // For serializable objects, first attempt to decode as a Parcelable if possible
  if (defaultValue is Parcelable) {
    return decodeParcelable(key, defaultValue::class.java as Class<Parcelable>) as? T
      ?: defaultValue
  }

  // Fall back to standard Java serialization (which MMKV doesn't directly support)
  val bytes = decodeBytes(key) ?: return defaultValue
  return try {
    ObjectInputStream(bytes.inputStream()).readObject() as? T ?: defaultValue
  } catch (e: Exception) {
    defaultValue
  }
}

/**
 * Helper function for encoding any Serializable objects
 */
fun MMKV.encodeSerializable(key: String, serializable: Serializable?) {
  if (serializable is Parcelable) {
    encode(key, serializable)
    return
  }

  // Fall back to standard Java serialization by converting to byte array
  try {
    val baos = ByteArrayOutputStream()
    val oos = ObjectOutputStream(baos)
    oos.writeObject(serializable)
    oos.close()
    encode(key, baos.toByteArray())
  } catch (e: Exception) {
    // Silently fail if serialization is not possible
  }
}

/**
 * Provides a StateFlow for any Parcelable object
 */
inline fun <reified T : Parcelable> MMKV.getParcelableStateFlow(
  key: String,
  defaultValue: T
): MutableStateFlow<T> =
  MMKVFlow(
    { decodeParcelable(key, defaultValue) ?: defaultValue },
    { encode(key, it as Parcelable) }
  )

/**
 * Provides a StateFlow for any Serializable object
 */
inline fun <reified T> MMKV.getSerializableStateFlow(
  key: String,
  defaultValue: T
): MutableStateFlow<T> where T : Serializable =
  MMKVFlow(
    { decodeSerializable(key, defaultValue) ?: defaultValue },
    {
      when (it) {
        is Parcelable -> encode(key, it as Parcelable)
        else -> encodeSerializable(key, it)
      }
    }
  )