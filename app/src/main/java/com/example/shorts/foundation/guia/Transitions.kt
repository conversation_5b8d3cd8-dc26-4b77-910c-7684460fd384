package com.example.shorts.foundation.guia

import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import com.roudikk.guia.animation.EnterExitTransition
import com.roudikk.guia.animation.NavTransition
import com.roudikk.guia.animation.to

object HorizontalSlideTransitions {

  fun buildSlideTransition(
    animationDuration: Int = 300
  ): NavTransition {
    val forward = EnterExitTransition(
      enter = slideInHorizontally(
        animationSpec = tween(animationDuration)
      ) { fullWidth -> fullWidth },
      exit = slideOutHorizontally(
        animationSpec = tween(animationDuration)
      ) { fullWidth -> -fullWidth }
    )

    val backward = EnterExitTransition(
      enter = slideInHorizontally(
        animationSpec = tween(animationDuration)
      ) { fullWidth -> -fullWidth },
      exit = slideOutHorizontally(
        animationSpec = tween(animationDuration)
      ) { fullWidth -> fullWidth }
    )

    return forward to backward
  }

  val Default = buildSlideTransition()
}

object FadeTransitions {

  fun createFadeTransition(
    animationDuration: Int = 300,
    initialAlpha: Float = 0f
  ): NavTransition {
    // 创建淡入淡出效果
    val fadeEffect = EnterExitTransition(
      // 淡入效果
      enter = fadeIn(
        animationSpec = tween(animationDuration),
        initialAlpha = initialAlpha
      ),
      // 淡出效果
      exit = fadeOut(
        animationSpec = tween(animationDuration),
        targetAlpha = initialAlpha
      )
    )

    return fadeEffect to fadeEffect
  }

  val Default = createFadeTransition()

  val FullFade = createFadeTransition(initialAlpha = 0f)

  val SemiFade = createFadeTransition(initialAlpha = 0.3f)

  val FastFullFade = createFadeTransition(animationDuration = 150, initialAlpha = 0f)
}
