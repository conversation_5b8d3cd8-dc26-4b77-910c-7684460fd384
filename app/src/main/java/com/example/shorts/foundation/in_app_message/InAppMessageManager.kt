package com.example.shorts.foundation.in_app_message

import android.app.Activity
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.ui.Modifier
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.ComposeView
import com.example.shorts.R
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.ui.theme.AppTheme
import org.aviran.cookiebar2.CookieBar
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

object InAppMessageManager : KoinComponent {
  private val inAppMessageKvStore: InAppMessageKvStore by inject()

  fun showPaypalWithdrawMessageIfCooldownPassed(
    activity: Activity,
    cooldownDuration: Duration,
    displayDuration: Duration = 3.seconds
  ) {
    val currentTime = System.currentTimeMillis()
    val lastShownTime = inAppMessageKvStore.lastPaypalWithdrawMessageShownTime
    val timeSinceLastShown = currentTime - lastShownTime

    if (timeSinceLastShown >= cooldownDuration.inWholeMilliseconds) {
      inAppMessageKvStore.lastPaypalWithdrawMessageShownTime = currentTime
      showPaypalWithdrawMessage(activity, displayDuration)
    }
  }

  fun showPaypalWithdrawMessage(
    activity: Activity,
    duration: Duration = 3.seconds
  ) {
    val tag = "showPaypalWithdrawMessage"

    CookieBar.build(activity)
      .setCustomView(R.layout.layout_in_app_message_container)
      .setCustomViewInitializer { view ->
        view.findViewById<ComposeView>(R.id.compose_view)?.apply {
          var totalDragY = 0f // 累积拖拽距离

          setContent {
            AppTheme {
              PaypalWithdrawMessage(
                modifier = Modifier
                  .statusBarsPadding()
                  .pointerInput(Unit) {
                    detectDragGestures(
                      onDragStart = { offset ->
                        totalDragY = 0f // 重置累积距离
                        debugLog(tag = tag) { "onDragStart: $offset" }
                      },
                      onDrag = { change, dragAmount ->
                        totalDragY += dragAmount.y // 累积Y方向的拖拽距离
                        debugLog(tag = tag) { "onDrag dragAmount: $dragAmount, totalDragY: $totalDragY" }
                        if (totalDragY < -50) { // 向上滑动累积超过50像素
                          CookieBar.dismiss(activity)
                          debugLog(tag = tag) { "CookieBar dismissed due to upward swipe" }
                        }
                      },
                      onDragEnd = {
                        debugLog(tag = tag) { "onDragEnd, totalDragY: $totalDragY" }
                      }
                    )
                  }
              )
            }
          }
        }
      }
      .setCookiePosition(CookieBar.TOP)
      .setEnableAutoDismiss(true)
      .setSwipeToDismiss(false)
      .setDuration(duration.inWholeMilliseconds)
      .show()
  }
}