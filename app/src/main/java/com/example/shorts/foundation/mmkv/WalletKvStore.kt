package com.example.shorts.foundation.mmkv

import android.content.Context
import com.example.shorts.foundation.coroutine.AppCoroutineScope
import com.example.shorts.ui.node.screen.wallet.WalletBalance
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import org.koin.core.annotation.Single

@Single
class WalletKvStore(
  context: Context,
  private val appCoroutineScope: AppCoroutineScope
) {
  companion object {
    const val TAG = "WalletKvStore"
  }

  private val mmkv by lazy { MMKV.mmkvWithID(TAG) }

  val amount = mmkv.getDoubleStateFlow(
    key = "amount",
    defaultValue = 0.00
  )
  val currencySymbol = mmkv.getStringStateFlow(
    key = "currencySymbol",
    defaultValue = "$"
  )
  val currencyCode = mmkv.getStringStateFlow(
    key = "currencyCode",
    defaultValue = "USD"
  )

  val balance: StateFlow<WalletBalance> = combine(
    amount,
    currencySymbol,
    currencyCode
  ) { amountValue, symbolValue, codeValue ->
    WalletBalance(
      amount = amountValue.toBigDecimal(),
      currencySymbol = symbolValue,
      currencyCode = codeValue
    )
  }.stateIn(
    scope = appCoroutineScope,
    started = SharingStarted.Eagerly,
    initialValue = WalletBalance(
      amount = amount.value.toBigDecimal(),
      currencySymbol = currencySymbol.value,
      currencyCode = currencyCode.value
    )
  )

  val walletAddress = mmkv.getStringStateFlow(
    key = "walletAddress",
    defaultValue = ""
  )
}