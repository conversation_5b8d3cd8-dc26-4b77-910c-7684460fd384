package com.example.shorts.foundation.guia

import androidx.compose.runtime.Composable
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.Screen
import com.roudikk.guia.extensions.requireLocalNavigator

abstract class ScreenNode(
  private val tag: String
) : NavigationKey.WithNode<Screen> {

  override fun tag() = tag

  override fun navigationNode() = Screen {
    Content(
      navigator = requireLocalNavigator(),
    )
  }

  @Composable
  abstract fun Content(
    navigator: Navigator,
  )
}
