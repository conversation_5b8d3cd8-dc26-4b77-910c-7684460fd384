package com.example.shorts.foundation.earn

import com.example.shorts.foundation.kermit.debugLog
import com.tencent.mmkv.MMKV
import org.koin.core.annotation.Single
import java.time.LocalDate

interface TaskRepository {
  fun getTasks(): List<Task>
  fun updateGlobalProgress(taskType: TaskType, newProgress: Int)
  fun claimReward(taskId: String)
  fun resetDailyTasks()
}

@Single(binds = [TaskRepository::class])
class TaskRepositoryImpl : TaskRepository {
  companion object {
    const val TAG = "TaskRepositoryImpl"
    private const val LAST_RESET_DATE_KEY = "task_last_reset_date"
    private const val WATCH_ADS_PROGRESS_KEY = "global_watch_ads_progress"
    private const val WATCH_DRAMA_PROGRESS_KEY = "global_watch_drama_progress"
  }

  private val mmkv by lazy { MMKV.mmkvWithID(TAG) }

  override fun getTasks(): List<Task> {
    checkAndResetDailyTasks()

    val adsProgress = getGlobalProgress(TaskType.WatchAds)
    val dramaProgress = getGlobalProgress(TaskType.WatchDrama)

    return TaskTemplates.map { template ->
      val instance = getOrCreateTaskInstance(template.id)

      // Calculate actual progress based on global progress
      val actualProgress = when (template.type) {
        TaskType.WatchAds -> minOf(adsProgress, template.goal)
        TaskType.WatchDrama -> minOf(dramaProgress, template.goal)
      }

      val updatedInstance = instance.copy(progress = actualProgress)
      Task(template, updatedInstance)
    }
  }


  override fun updateGlobalProgress(taskType: TaskType, newProgress: Int) {
    val key = when (taskType) {
      TaskType.WatchAds -> WATCH_ADS_PROGRESS_KEY
      TaskType.WatchDrama -> WATCH_DRAMA_PROGRESS_KEY
    }
    mmkv.encode(key, newProgress)
  }

  override fun claimReward(taskId: String) {
    val instance = getOrCreateTaskInstance(taskId)
    val updatedInstance = instance.copy(rewardClaimed = true)
    saveTaskInstance(taskId, updatedInstance)
  }

  override fun resetDailyTasks() {
    debugLog(tag = TAG) { "resetDailyTasks" }
    val today = LocalDate.now()

    // Reset global progress counters
    mmkv.encode(WATCH_ADS_PROGRESS_KEY, 0)
    mmkv.encode(WATCH_DRAMA_PROGRESS_KEY, 0)

    // Reset all reward claims (but not individual completed since we use global progress now)
    TaskTemplates.forEach { template ->
      val instance = getOrCreateTaskInstance(template.id)
      val resetInstance = instance.copy(rewardClaimed = false)
      saveTaskInstance(template.id, resetInstance)
    }

    // Update global reset date
    mmkv.encode(LAST_RESET_DATE_KEY, today.toString())
  }

  private fun checkAndResetDailyTasks() {
    val today = LocalDate.now()
    val lastResetDateString = mmkv.decodeString(LAST_RESET_DATE_KEY, "1970-01-01")
    val lastResetDate = LocalDate.parse(lastResetDateString)

    if (lastResetDate.isBefore(today)) {
      resetDailyTasks()
    }
  }

  private fun getGlobalProgress(taskType: TaskType): Int {
    val key = when (taskType) {
      TaskType.WatchAds -> WATCH_ADS_PROGRESS_KEY
      TaskType.WatchDrama -> WATCH_DRAMA_PROGRESS_KEY
    }
    return mmkv.decodeInt(key, 0)
  }

  private fun getOrCreateTaskInstance(taskId: String): TaskInstance {
    val progressKey = "task_${taskId}_progress"
    val enabledKey = "task_${taskId}_enabled"
    val rewardClaimedKey = "task_${taskId}_reward_claimed"

    val progress = mmkv.decodeInt(progressKey, 0)
    val enabled = mmkv.decodeBool(enabledKey, true)
    val rewardClaimed = mmkv.decodeBool(rewardClaimedKey, false)

    return TaskInstance(
      templateId = taskId,
      progress = progress,
      enabled = enabled,
      rewardClaimed = rewardClaimed
    )
  }

  private fun saveTaskInstance(taskId: String, instance: TaskInstance) {
    val progressKey = "task_${taskId}_progress"
    val enabledKey = "task_${taskId}_enabled"
    val rewardClaimedKey = "task_${taskId}_reward_claimed"

    mmkv.encode(progressKey, instance.progress)
    mmkv.encode(enabledKey, instance.enabled)
    mmkv.encode(rewardClaimedKey, instance.rewardClaimed)
  }
}