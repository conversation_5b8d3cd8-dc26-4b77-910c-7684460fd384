package com.example.shorts.foundation.checkin

import com.example.shorts.foundation.kermit.debugLog
import com.tencent.mmkv.MMKV
import org.koin.core.annotation.Single
import java.time.LocalDate

@Single(binds = [CheckInRepository::class])
class CheckInRepositoryImpl : CheckInRepository {
  companion object {
    const val TAG = "CheckInRepository"
    private const val LAST_CHECKIN_DATE_KEY = "checkin_last_date"
    private const val CONSECUTIVE_DAYS_KEY = "checkin_consecutive_days"
  }

  private val mmkv by lazy { MMKV.mmkvWithID(TAG) }

  override fun getCheckInState(): CheckInState {
    val today = LocalDate.now()
    val lastDateString = mmkv.decodeString(LAST_CHECKIN_DATE_KEY, null)
    val lastDate = lastDateString?.let { LocalDate.parse(it) }
    val storedConsecutive = mmkv.decodeInt(CONSECUTIVE_DAYS_KEY, 0)

    val (currentDay, todayCheckedIn, displayConsecutiveDays) = when {
      lastDate == null -> Triple(1, false, 0) // 首次使用
      lastDate == today -> Triple(storedConsecutive, true, storedConsecutive) // 今天已签到
      lastDate == today.minusDays(1) -> {
        // 连续签到，显示昨天完成的连续天数
        val nextDay = if (storedConsecutive >= 7) 1 else storedConsecutive + 1
        Triple(nextDay, false, storedConsecutive)
      }
      else -> {
        // 断签重置，清除连续天数
        mmkv.encode(CONSECUTIVE_DAYS_KEY, 0)
        Triple(1, false, 0)
      }
    }

    return CheckInState(
      consecutiveDays = displayConsecutiveDays,
      todayCheckedIn = todayCheckedIn,
      lastCheckInDate = lastDate,
      currentDay = currentDay
    )
  }

  override fun checkIn(): CheckInResult {
    val state = getCheckInState()

    if (state.todayCheckedIn) {
      return CheckInResult.AlreadyCheckedIn
    }

    val today = LocalDate.now()
    val checkInDay = state.currentDay
    val reward = DefaultCheckInRewards[checkInDay - 1]
    val isNewCycle = checkInDay == 1 && state.lastCheckInDate != null

    // 保存签到记录
    mmkv.encode(LAST_CHECKIN_DATE_KEY, today.toString())
    mmkv.encode(CONSECUTIVE_DAYS_KEY, checkInDay)

    debugLog(tag = TAG) { "checkIn: day=$checkInDay, reward=${reward.reward}, isNewCycle=$isNewCycle" }

    return CheckInResult.Success(
      day = checkInDay,
      reward = reward,
      isNewCycle = isNewCycle
    )
  }

  override fun getCheckInRewards(): List<CheckInReward> {
    return DefaultCheckInRewards
  }
}