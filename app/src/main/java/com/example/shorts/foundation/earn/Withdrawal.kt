package com.example.shorts.foundation.earn

import android.os.Parcelable
import com.example.shorts.foundation.number.formatWithCommas
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.math.BigDecimal

@Parcelize
data class Withdrawal(
  val amount: BigDecimal,
  val currencySymbol: String
) : Parcelable {
  @IgnoredOnParcel
  val amountWithCommas get() = amount.formatWithCommas()

  @IgnoredOnParcel
  val displayAmount get() = "${currencySymbol}${amountWithCommas}"
}

val WithdrawalOptions =
  listOf(
    Withdrawal(BigDecimal(7000.00), "$"),
    Withdrawal(BigDecimal(12000), "$"),
    Withdrawal(BigDecimal(20000), "$"),
    Withdrawal(BigDecimal(30000), "$"),
    Withdrawal(BigDecimal(46000), "$")
  )