package com.example.shorts.foundation.mvi_ui_model

import androidx.compose.runtime.Composable
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.CreationExtras
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import org.koin.androidx.compose.koinViewModel
import org.koin.compose.currentKoinScope
import org.koin.core.parameter.ParametersDefinition
import org.koin.core.qualifier.Qualifier
import org.koin.core.scope.Scope
import org.koin.viewmodel.defaultExtras
import org.orbitmvi.orbit.Container
import org.orbitmvi.orbit.ContainerHost
import org.orbitmvi.orbit.viewmodel.container

abstract class UiModel<STATE : Any, SIDE_EFFECT : Any>(state: STATE) : ViewModel(),
  ContainerHost<STATE, SIDE_EFFECT> {

  override val container: Container<STATE, SIDE_EFFECT> = container(state)

  val uiModelScope get() = viewModelScope
}

typealias UiModelStoreOwner = ViewModelStoreOwner

@Composable
inline fun <reified T : ViewModel> koinUiModel(
  qualifier: Qualifier? = null,
  uiModelStoreOwner: UiModelStoreOwner = checkNotNull(LocalViewModelStoreOwner.current) {
    "No ViewModelStoreOwner was provided via LocalViewModelStoreOwner"
  },
  key: String? = null,
  extras: CreationExtras = defaultExtras(uiModelStoreOwner),
  scope: Scope = currentKoinScope(),
  noinline parameters: ParametersDefinition? = null,
) = koinViewModel<T>(
  qualifier, uiModelStoreOwner, key, extras, scope, parameters
)
