package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.VideoFilled: ImageVector
  get() {
    if (_VideoFilled != null) {
      return _VideoFilled!!
    }
    _VideoFilled = ImageVector.Builder(
      name = "VideoFilled",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFD469FF),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(35f, 17.745f),
          end = Offset(26.202f, 51.178f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(60.694f, 24.054f)
        lineTo(60.694f, 52.5f)
        curveTo(60.694f, 55.261f, 58.455f, 57.5f, 55.694f, 57.5f)
        lineTo(14.306f, 57.5f)
        curveTo(11.545f, 57.5f, 9.306f, 55.261f, 9.306f, 52.5f)
        lineTo(9.306f, 24.054f)
        lineTo(60.694f, 24.054f)
        close()
        moveTo(29.864f, 30.413f)
        curveTo(28.759f, 30.413f, 27.864f, 31.308f, 27.864f, 32.413f)
        lineTo(27.864f, 44.606f)
        curveTo(27.864f, 44.979f, 27.968f, 45.344f, 28.165f, 45.661f)
        curveTo(28.748f, 46.6f, 29.981f, 46.888f, 30.919f, 46.305f)
        lineTo(40.73f, 40.208f)
        curveTo(40.991f, 40.046f, 41.211f, 39.826f, 41.373f, 39.565f)
        curveTo(41.956f, 38.627f, 41.668f, 37.394f, 40.73f, 36.811f)
        lineTo(30.919f, 30.714f)
        curveTo(30.602f, 30.517f, 30.237f, 30.413f, 29.864f, 30.413f)
        close()
        moveTo(27.832f, 12.5f)
        lineTo(22.319f, 22.053f)
        lineTo(9.306f, 22.054f)
        lineTo(9.306f, 17.5f)
        curveTo(9.306f, 14.739f, 11.545f, 12.5f, 14.306f, 12.5f)
        lineTo(27.832f, 12.5f)
        close()
        moveTo(55.694f, 12.5f)
        curveTo(58.455f, 12.5f, 60.694f, 14.739f, 60.694f, 17.5f)
        lineTo(60.694f, 22.054f)
        lineTo(41.997f, 22.053f)
        lineTo(47.221f, 13f)
        curveTo(47.312f, 12.842f, 47.355f, 12.67f, 47.355f, 12.5f)
        lineTo(55.694f, 12.5f)
        close()
        moveTo(45.199f, 12.5f)
        lineTo(39.69f, 22.053f)
        lineTo(24.625f, 22.053f)
        lineTo(29.853f, 13f)
        curveTo(29.945f, 12.842f, 29.988f, 12.669f, 29.988f, 12.499f)
        lineTo(45.199f, 12.5f)
        close()
      }
    }.build()

    return _VideoFilled!!
  }

@Suppress("ObjectPropertyName")
private var _VideoFilled: ImageVector? = null

@Preview
@Composable
private fun VideoFilledPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.VideoFilled, contentDescription = null)
  }
}
