package com.example.shorts.ui.node.screen.home.discover

import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.mvi_ui_model.UiModel
import kotlinx.coroutines.delay
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class DiscoverUiModel : UiModel<DiscoverUiState, Nothing>(DiscoverUiState()) {

  companion object {
    const val TAG = "DiscoverUiModel"
  }

  init {
    onLoad()
  }

  fun onLoad() = intent {
    debugLog(tag = TAG) { "onLoad" }
    loadInitialData()
  }

  fun onCategorySelected(categoryId: Long) = intent {
    debugLog(tag = TAG) { "onCategorySelected: $categoryId" }
    if (state.selectedCategoryId == categoryId) return@intent

    reduce { state.copy(selectedCategoryId = categoryId) }

    // Load category dramas if not cached
    if (!state.categoryDramasCache.containsKey(categoryId)) {
      loadCategoryDramas(categoryId, isFirstPage = true)
    }
  }

  fun onLoadMoreCategoryDramas() = intent {
    val categoryId = state.selectedCategoryId ?: return@intent
    val cache = state.categoryDramasCache[categoryId] ?: return@intent

    if (cache.isLoading || !cache.hasMore) return@intent

    debugLog(tag = TAG) { "onLoadMoreCategoryDramas: categoryId=$categoryId, page=${cache.currentPage + 1}" }
    loadCategoryDramas(categoryId, isFirstPage = false)
  }

  private fun loadInitialData() = intent {
    reduce { state.copy(isLoading = true, error = null) }
    
    try {
      // Wait for SDK initialization
      while (!PSSDK.hasInitialized()) {
        delay(100)
      }

      // Load recommended dramas
      loadRecommendedDramas()
      
      // Load new releases
      loadNewReleases()
      
      // Load categories
      loadCategories()
      
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Failed to load initial data: ${e.message}" }
      reduce {
        state.copy(
          isLoading = false,
          error = e.message ?: "Unknown error"
        )
      }
    }
  }

  private fun loadRecommendedDramas() = intent {
    try {
      PSSDK.requestPopularDrama(1, 10, object : PSSDK.FeedListResultListener {
        override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
          intent {
            reduce {
              state.copy(
                recommendedDramas = result.dataList.take(10),
                isLoading = false
              )
            }
          }
        }

        override fun onFail(errorInfo: PSSDK.ErrorInfo) {
          debugLog(tag = TAG) { "Failed to load recommended dramas: ${errorInfo.code} - ${errorInfo.msg}" }
          intent {
            reduce {
              state.copy(
                isLoading = false,
                error = errorInfo.msg ?: "Failed to load recommended dramas"
              )
            }
          }
        }
      })
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Exception loading recommended dramas: ${e.message}" }
      reduce {
        state.copy(
          isLoading = false,
          error = e.message ?: "Unknown error"
        )
      }
    }
  }

  private fun loadNewReleases() = intent {
    try {
      PSSDK.requestNewDrama(1, 10, object : PSSDK.FeedListResultListener {
        override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
          intent {
            reduce {
              state.copy(newReleases = result.dataList.take(10))
            }
          }
        }

        override fun onFail(errorInfo: PSSDK.ErrorInfo) {
          debugLog(tag = TAG) { "Failed to load new releases: ${errorInfo.code} - ${errorInfo.msg}" }
        }
      })
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Exception loading new releases: ${e.message}" }
    }
  }

  private fun loadCategories() = intent {
    try {
      reduce { state.copy(isLoadingCategories = true) }
      
      val contentLanguages = PSSDK.getContentLanguages()
      val language = if (contentLanguages?.isNotEmpty() == true) contentLanguages[0] else ""

      PSSDK.requestCategoryList(language, object : PSSDK.CategoryListResultListener {
        override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay.ShortPlayCategory>) {
          intent {
            // Filter categories that have content
            val validCategories = result.dataList.filter { category ->
              category.count > 0 && !category.name.isNullOrEmpty()
            }

            reduce {
              state.copy(
                categories = validCategories,
                isLoadingCategories = false,
                selectedCategoryId = validCategories.firstOrNull()?.id
              )
            }

            // Load dramas for first category
            validCategories.firstOrNull()?.id?.let { categoryId ->
              loadCategoryDramas(categoryId, isFirstPage = true)
            }
          }
        }

        override fun onFail(errorInfo: PSSDK.ErrorInfo) {
          debugLog(tag = TAG) { "Failed to load categories: ${errorInfo.code} - ${errorInfo.msg}" }
          intent {
            reduce {
              state.copy(isLoadingCategories = false)
            }
          }
        }
      })
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Exception loading categories: ${e.message}" }
      reduce {
        state.copy(isLoadingCategories = false)
      }
    }
  }

  private fun loadCategoryDramas(categoryId: Long, isFirstPage: Boolean) = intent {
    try {
      val currentCache = state.categoryDramasCache[categoryId] ?: CategoryDramaCache()
      val pageToLoad = if (isFirstPage) 1 else currentCache.currentPage + 1

      // Update loading state
      val updatedCache = currentCache.copy(isLoading = true)
      reduce {
        state.copy(
          categoryDramasCache = state.categoryDramasCache + (categoryId to updatedCache)
        )
      }

      val categoryIds = arrayListOf(categoryId)
      PSSDK.requestFeedListByCategoryIds(
        categoryIds,
        null,
        pageToLoad,
        20,
        object : PSSDK.FeedListResultListener {
          override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
            intent {
              val existingCache = state.categoryDramasCache[categoryId] ?: CategoryDramaCache()
              val newDramas = if (isFirstPage) {
                result.dataList
              } else {
                existingCache.dramas + result.dataList
              }

              val finalCache = existingCache.copy(
                dramas = newDramas,
                currentPage = pageToLoad,
                hasMore = result.hasMore,
                isLoading = false
              )

              reduce {
                state.copy(
                  categoryDramasCache = state.categoryDramasCache + (categoryId to finalCache)
                )
              }
            }
          }

          override fun onFail(errorInfo: PSSDK.ErrorInfo) {
            debugLog(tag = TAG) { "Failed to load category dramas: ${errorInfo.code} - ${errorInfo.msg}" }
            intent {
              val existingCache = state.categoryDramasCache[categoryId] ?: CategoryDramaCache()
              val failedCache = existingCache.copy(isLoading = false)

              reduce {
                state.copy(
                  categoryDramasCache = state.categoryDramasCache + (categoryId to failedCache)
                )
              }
            }
          }
        }
      )
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Exception loading category dramas: ${e.message}" }
      val existingCache = state.categoryDramasCache[categoryId] ?: CategoryDramaCache()
      val errorCache = existingCache.copy(isLoading = false)

      reduce {
        state.copy(
          categoryDramasCache = state.categoryDramasCache + (categoryId to errorCache)
        )
      }
    }
  }
}
