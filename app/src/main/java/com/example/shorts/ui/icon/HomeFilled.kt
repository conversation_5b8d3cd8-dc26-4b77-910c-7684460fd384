package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.HomeFilled: ImageVector
  get() {
    if (_HomeFilled != null) {
      return _HomeFilled!!
    }
    _HomeFilled = ImageVector.Builder(
      name = "HomeFilled",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0.007f to Color(0xFFD364FF),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(35f, 10.309f),
          end = Offset(28.352f, 57.74f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(38.316f, 11.567f)
        lineTo(56.311f, 27.509f)
        curveTo(57.168f, 28.269f, 57.658f, 29.359f, 57.658f, 30.503f)
        lineTo(57.658f, 52.266f)
        curveTo(57.658f, 56.132f, 54.524f, 59.266f, 50.658f, 59.266f)
        lineTo(41.038f, 59.266f)
        lineTo(41.038f, 49.112f)
        curveTo(41.038f, 46.35f, 38.799f, 44.112f, 36.038f, 44.112f)
        lineTo(33.962f, 44.112f)
        curveTo(31.201f, 44.112f, 28.962f, 46.35f, 28.962f, 49.112f)
        lineTo(28.962f, 59.266f)
        lineTo(19.342f, 59.266f)
        curveTo(15.476f, 59.266f, 12.342f, 56.132f, 12.342f, 52.266f)
        lineTo(12.342f, 30.503f)
        curveTo(12.342f, 29.359f, 12.832f, 28.269f, 13.689f, 27.509f)
        lineTo(31.684f, 11.567f)
        curveTo(33.577f, 9.89f, 36.423f, 9.89f, 38.316f, 11.567f)
        close()
      }
    }.build()

    return _HomeFilled!!
  }

@Suppress("ObjectPropertyName")
private var _HomeFilled: ImageVector? = null

@Preview
@Composable
private fun HomeFilledPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.HomeFilled, contentDescription = null)
  }
}
