package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.R
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.earn.WithdrawalOptions
import com.example.shorts.ui.theme.AppTheme

/**
 * 提现金额选择器组件
 */
@Composable
fun WithdrawAmountSelector(
  title: String = LocalStrings.current.selectWithdrawalAmount,
  withdrawals: List<Withdrawal>,
  selectedWithdrawal: Withdrawal?,
  onSelect: (Withdrawal) -> Unit,
  modifier: Modifier = Modifier
) {
  Column(
    modifier = modifier
  ) {
    // 标题
    Text(
      text = title,
      fontSize = 14.sp,
      fontWeight = FontWeight.Medium,
      color = Color.White,
      modifier = Modifier.padding(bottom = 4.dp)
    )

    // 金额选项网格
    FlowRow(
      horizontalArrangement = Arrangement.spacedBy(8.dp),
      verticalArrangement = Arrangement.spacedBy(8.dp),
      modifier = Modifier.fillMaxWidth(),
      maxItemsInEachRow = 3
    ) {
      withdrawals.forEach { option ->
        WithdrawalButton(
          withdrawal = option,
          selected = selectedWithdrawal == option,
          onClick = { onSelect(option) },
          modifier = Modifier.weight(1f)
        )
      }

      Spacer(Modifier.weight(1f))
    }
  }
}

/**
 * 单个提现金额按钮
 */
@Composable
private fun WithdrawalButton(
  withdrawal: Withdrawal,
  selected: Boolean,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val (backgroundColor, borderColor) = if (selected) {
    val brightenPrimary = MaterialTheme.colorScheme.primary.brighten()
    brightenPrimary.copy(.2f) to brightenPrimary
  } else {
    MaterialTheme.colorScheme.surface to Color.Transparent
  }

  Box(
    modifier = modifier
      .height(56.dp)
      .clip(RoundedCornerShape(12.dp))
      .background(color = backgroundColor)
      .border(
        width = 1.dp,
        color = borderColor,
        shape = RoundedCornerShape(12.dp)
      )
      .clickable { onClick() },
    contentAlignment = Alignment.Center
  ) {
    Row(
      verticalAlignment = Alignment.CenterVertically,
      horizontalArrangement = Arrangement.Center,
      modifier = Modifier.basicMarquee()
    ) {
      Image(
        painter = painterResource(R.drawable.ic_cash_64px),
        contentDescription = null,
        modifier = Modifier.size(16.dp)
      )

      // 金额文本
      Text(
        text = withdrawal.displayAmount,
        fontSize = 13.sp,
        fontWeight = FontWeight.Medium,
        color = if (selected) Color.White else Color.White.copy(alpha = 0.9f),
        maxLines = 1,
        modifier = Modifier.padding(start = 4.dp, end = 8.dp)
      )
    }
  }
}

@Preview
@Composable
fun WithdrawAmountSelectorPreview() {
  AppTheme {
    WithdrawAmountSelector(
      withdrawals = WithdrawalOptions,
      selectedWithdrawal = WithdrawalOptions.first(),
      onSelect = { }
    )
  }
}
