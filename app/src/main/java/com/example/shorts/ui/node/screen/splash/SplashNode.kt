package com.example.shorts.ui.node.screen.splash

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import coil3.compose.AsyncImage
import com.example.shorts.R
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.ui.node.screen.home.HomeNode
import com.example.shorts.ui.theme.AppTheme
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.popToRoot
import com.roudikk.guia.extensions.replaceLast
import com.roudikk.guia.extensions.setRoot
import com.skydoves.compose.effects.RememberedEffect
import kotlinx.coroutines.delay
import kotlinx.parcelize.Parcelize

@Parcelize
class SplashNode : ScreenNode("splash") {
  @Composable
  override fun Content(navigator: Navigator) {

    LaunchedEffect(Unit) {
      delay(1_000)
      navigator.run {
        val newRootNode = HomeNode()
        setRoot(newRootNode)
        replaceLast(newRootNode)
      }
    }

    SplashContent()
  }
}

@Composable
private fun SplashContent() {
  val strings = LocalStrings.current

  Column(
    modifier = Modifier
      .fillMaxSize()
      .background(MaterialTheme.colorScheme.background),
    horizontalAlignment = Alignment.CenterHorizontally,
    verticalArrangement = Arrangement.SpaceBetween
  ) {
    Spacer(modifier = Modifier.weight(1f))

    // App Icon and Name Section
    Column(
      horizontalAlignment = Alignment.CenterHorizontally,
      verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
      // App Icon
      AsyncImage(
        model = R.drawable.ic_app_launcher,
        contentDescription = "App Icon",
        modifier = Modifier
          .size(88.dp)
          .clip(RoundedCornerShape(24.dp))
      )

      // App Name
      Text(
        text = strings.appName,
        style = MaterialTheme.typography.titleLarge,
        fontWeight = FontWeight.Bold,
      )
    }

    Spacer(modifier = Modifier.weight(1f))

    // Bottom Section with Loading Indicator
    Column(
      horizontalAlignment = Alignment.CenterHorizontally,
      verticalArrangement = Arrangement.spacedBy(12.dp),
      modifier = Modifier
        .padding(bottom = 60.dp)
        .navigationBarsPadding()
    ) {
      // Loading Indicator
      LinearProgressIndicator(
        modifier = Modifier
          .fillMaxWidth(.8f)
          .height(12.dp)
          .clip(CircleShape),
        color = MaterialTheme.colorScheme.primary,
        trackColor = MaterialTheme.colorScheme.surfaceVariant
      )

      // Loading Text
      Text(
        text = strings.splashLoadingText,
        style = MaterialTheme.typography.bodySmall,
        modifier = Modifier.alpha(.8f)
      )
    }
  }
}


@Preview
@Composable
private fun SplashContentPreview() {
  AppTheme {
    SplashContent()
  }
}