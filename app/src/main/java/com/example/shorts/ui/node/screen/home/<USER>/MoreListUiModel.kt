package com.example.shorts.ui.node.screen.home.discover

import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.mvi_ui_model.UiModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import kotlin.coroutines.suspendCoroutine

@KoinViewModel
class MoreListUiModel(
  private val config: MoreListConfig
) : UiModel<MoreListUiState, Nothing>(MoreListUiState(config = config)) {

  companion object {
    private const val PAGE_SIZE = 20
  }

  init {
    loadInitialData()
  }

  private fun loadInitialData() = intent {
    reduce { state.copy(isLoading = true, error = null) }
    
    try {
      val result = when (config.type) {
        MoreListType.RECOMMENDED -> requestFeedList(1, PAGE_SIZE)
        MoreListType.NEW_RELEASES -> requestNewDrama(1, PAGE_SIZE)
        MoreListType.CATEGORY -> requestCategoryDramas(1, PAGE_SIZE)
      }
      
      reduce {
        state.copy(
          dramas = result.dataList,
          isLoading = false,
          hasMore = result.hasMore,
          currentPage = 1
        )
      }
    } catch (e: Exception) {
      reduce {
        state.copy(
          isLoading = false,
          error = e.message ?: "Failed to load data"
        )
      }
    }
  }

  fun loadMore() = intent {
    if (state.isLoadingMore || !state.hasMore) return@intent
    
    reduce { state.copy(isLoadingMore = true) }
    
    try {
      val nextPage = state.currentPage + 1
      val result = when (config.type) {
        MoreListType.RECOMMENDED -> requestFeedList(nextPage, PAGE_SIZE)
        MoreListType.NEW_RELEASES -> requestNewDrama(nextPage, PAGE_SIZE)
        MoreListType.CATEGORY -> requestCategoryDramas(nextPage, PAGE_SIZE)
      }
      
      reduce {
        state.copy(
          dramas = state.dramas + result.dataList,
          isLoadingMore = false,
          hasMore = result.hasMore,
          currentPage = nextPage
        )
      }
    } catch (e: Exception) {
      reduce {
        state.copy(
          isLoadingMore = false,
          error = e.message ?: "Failed to load more data"
        )
      }
    }
  }

  fun retry() = intent {
    if (state.dramas.isEmpty()) {
      loadInitialData()
    } else {
      loadMore()
    }
  }

  private suspend fun requestFeedList(page: Int, count: Int): PSSDK.FeedListLoadResult<ShortPlay> {
    return withContext(Dispatchers.IO) {
      suspendCoroutine { continuation ->
        PSSDK.requestFeedList(page, count, object : PSSDK.FeedListResultListener {
          override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
            continuation.resumeWith(Result.success(result))
          }

          override fun onFail(errorInfo: PSSDK.ErrorInfo) {
            continuation.resumeWith(
              Result.failure(Exception("${errorInfo.code}: ${errorInfo.msg}"))
            )
          }
        })
      }
    }
  }

  private suspend fun requestNewDrama(page: Int, count: Int): PSSDK.FeedListLoadResult<ShortPlay> {
    return withContext(Dispatchers.IO) {
      suspendCoroutine { continuation ->
        PSSDK.requestNewDrama(page, count, object : PSSDK.FeedListResultListener {
          override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
            continuation.resumeWith(Result.success(result))
          }

          override fun onFail(errorInfo: PSSDK.ErrorInfo) {
            continuation.resumeWith(
              Result.failure(Exception("${errorInfo.code}: ${errorInfo.msg}"))
            )
          }
        })
      }
    }
  }

  private suspend fun requestCategoryDramas(page: Int, count: Int): PSSDK.FeedListLoadResult<ShortPlay> {
    return withContext(Dispatchers.IO) {
      suspendCoroutine { continuation ->
        val category = config.category ?: throw IllegalStateException("Category is required for category type")
        val categoryIds = arrayListOf(category.id)

        PSSDK.requestFeedListByCategoryIds(categoryIds, null, page, count, object : PSSDK.FeedListResultListener {
          override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
            continuation.resumeWith(Result.success(result))
          }

          override fun onFail(errorInfo: PSSDK.ErrorInfo) {
            continuation.resumeWith(
              Result.failure(Exception("${errorInfo.code}: ${errorInfo.msg}"))
            )
          }
        })
      }
    }
  }
}
