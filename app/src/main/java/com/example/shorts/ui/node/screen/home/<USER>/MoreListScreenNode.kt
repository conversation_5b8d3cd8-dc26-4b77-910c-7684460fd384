package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import coil3.compose.AsyncImage
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState

enum class MoreListType {
  RECOMMENDED,
  NEW_RELEASES
}

@Parcelize
class MoreListScreenNode(
  private val listType: MoreListType
) : ScreenNode("more_list_screen") {

  @Composable
  override fun Content(navigator: Navigator) {
    val uiModel: MoreListUiModel = koinUiModel { 
      parametersOf(listType)
    }
    val uiState by uiModel.collectAsState()

    MoreListScreen(
      uiState = uiState,
      listType = listType,
      onBackClick = { navigator.pop() },
      onDramaClick = { drama ->
        navigator.push(DramaPlayScreenNode(drama))
      },
      onLoadMore = uiModel::loadMore,
      onRetry = uiModel::retry
    )
  }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MoreListScreen(
  uiState: MoreListUiState,
  listType: MoreListType,
  onBackClick: () -> Unit,
  onDramaClick: (ShortPlay) -> Unit,
  onLoadMore: () -> Unit,
  onRetry: () -> Unit
) {
  val strings = LocalStrings.current
  val listState = rememberLazyListState()

  // Detect when user scrolls near the bottom to trigger pagination
  LaunchedEffect(listState) {
    snapshotFlow { 
      listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index 
    }.collect { lastVisibleIndex ->
      if (lastVisibleIndex != null && 
          lastVisibleIndex >= uiState.dramas.size - 3 && 
          !uiState.isLoadingMore && 
          uiState.hasMore) {
        onLoadMore()
      }
    }
  }

  Column(modifier = Modifier.fillMaxSize()) {
    // Top App Bar
    TopAppBar(
      title = {
        Text(
          text = when (listType) {
            MoreListType.RECOMMENDED -> strings.recommendedForYou
            MoreListType.NEW_RELEASES -> strings.newReleases
          },
          fontSize = 18.sp,
          fontWeight = FontWeight.Medium
        )
      },
      navigationIcon = {
        IconButton(onClick = onBackClick) {
          Icon(
            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
            contentDescription = "Back"
          )
        }
      }
    )

    when {
      uiState.isLoading && uiState.dramas.isEmpty() -> {
        Box(
          modifier = Modifier.fillMaxSize(),
          contentAlignment = Alignment.Center
        ) {
          CircularProgressIndicator()
        }
      }

      uiState.error != null && uiState.dramas.isEmpty() -> {
        Box(
          modifier = Modifier.fillMaxSize(),
          contentAlignment = Alignment.Center
        ) {
          Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
          ) {
            Text(
              text = uiState.error,
              style = MaterialTheme.typography.bodyLarge,
              color = MaterialTheme.colorScheme.error
            )
            Button(onClick = onRetry) {
              Text("Retry")
            }
          }
        }
      }

      else -> {
        LazyColumn(
          state = listState,
          modifier = Modifier.fillMaxSize(),
          contentPadding = PaddingValues(16.dp),
          verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
          items(
            items = uiState.dramas.chunked(2), // Group items in pairs for 2-column layout
            key = { dramasPair -> dramasPair.firstOrNull()?.id ?: 0 }
          ) { dramasPair ->
            Row(
              modifier = Modifier.fillMaxWidth(),
              horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
              dramasPair.forEach { drama ->
                DramaGridItem(
                  drama = drama,
                  onClick = { onDramaClick(drama) },
                  modifier = Modifier.weight(1f)
                )
              }
              // Fill empty space if odd number of items
              if (dramasPair.size == 1) {
                Spacer(modifier = Modifier.weight(1f))
              }
            }
          }

          // Loading more indicator
          if (uiState.isLoadingMore) {
            item {
              Box(
                modifier = Modifier
                  .fillMaxWidth()
                  .padding(16.dp),
                contentAlignment = Alignment.Center
              ) {
                CircularProgressIndicator()
              }
            }
          }

          // End of list indicator
          if (!uiState.hasMore && uiState.dramas.isNotEmpty()) {
            item {
              Box(
                modifier = Modifier
                  .fillMaxWidth()
                  .padding(16.dp),
                contentAlignment = Alignment.Center
              ) {
                Text(
                  text = "No more content",
                  style = MaterialTheme.typography.bodyMedium,
                  color = Color.Gray
                )
              }
            }
          }
        }
      }
    }
  }
}

@Composable
private fun DramaGridItem(
  drama: ShortPlay,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Card(
    modifier = modifier
      .fillMaxWidth()
      .clickable { onClick() },
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .aspectRatio(3 / 4f),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier
          .padding(vertical = 8.dp, horizontal = 8.dp)
      ) {
        Text(
          text = drama.title + "\n",
          fontSize = 13.sp,
          lineHeight = 17.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
        )

        Text(
          text = strings.episodesCount(drama.total),
          fontSize = 10.sp,
          lineHeight = 14.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 3.dp)
        )
      }
    }
  }
}
