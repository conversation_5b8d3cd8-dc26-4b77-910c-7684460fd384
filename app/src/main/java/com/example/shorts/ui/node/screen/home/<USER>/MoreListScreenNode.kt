package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import coil3.compose.AsyncImage
import dev.chrisbanes.haze.HazeDefaults
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import kotlinx.parcelize.Parcelize
import org.koin.core.parameter.parametersOf
import org.orbitmvi.orbit.compose.collectAsState
import android.os.Parcelable

enum class MoreListType {
  RECOMMENDED,
  NEW_RELEASES,
  CATEGORY
}

@Parcelize
data class MoreListConfig(
  val type: MoreListType,
  val categoryId: Long? = null,
  val categoryName: String? = null
) : Parcelable {
  companion object {
    fun recommended() = MoreListConfig(MoreListType.RECOMMENDED)
    fun newReleases() = MoreListConfig(MoreListType.NEW_RELEASES)
    fun category(categoryId: Long, categoryName: String) = MoreListConfig(
      type = MoreListType.CATEGORY,
      categoryId = categoryId,
      categoryName = categoryName
    )
  }
}

@Parcelize
class MoreListScreenNode(
  private val config: MoreListConfig
) : ScreenNode("more_list_screen") {

  // Backward compatibility constructors
  constructor(listType: MoreListType) : this(
    when (listType) {
      MoreListType.RECOMMENDED -> MoreListConfig.recommended()
      MoreListType.NEW_RELEASES -> MoreListConfig.newReleases()
      MoreListType.CATEGORY -> throw IllegalArgumentException("Category type requires categoryId and categoryName")
    }
  )

  @Composable
  override fun Content(navigator: Navigator) {
    val uiModel: MoreListUiModel = koinUiModel { parametersOf(config) }
    val uiState by uiModel.collectAsState()

    MoreListScreen(
      uiState = uiState,
      config = config,
      onBackClick = { navigator.pop() },
      onDramaClick = { drama ->
        navigator.push(DramaPlayScreenNode(drama))
      },
      onLoadMore = uiModel::loadMore,
      onRetry = uiModel::retry
    )
  }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MoreListScreen(
  uiState: MoreListUiState,
  config: MoreListConfig,
  onBackClick: () -> Unit,
  onDramaClick: (ShortPlay) -> Unit,
  onLoadMore: () -> Unit,
  onRetry: () -> Unit
) {
  val strings = LocalStrings.current
  val listState = rememberLazyListState()
  val hazeState = rememberHazeState()

  // Detect when user scrolls near the bottom to trigger pagination
  LaunchedEffect(listState) {
    snapshotFlow {
      listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index
    }.collect { lastVisibleIndex ->
      if (lastVisibleIndex != null &&
        lastVisibleIndex >= uiState.dramas.size - 3 &&
        !uiState.isLoadingMore &&
        uiState.hasMore
      ) {
        onLoadMore()
      }
    }
  }

  Column(modifier = Modifier.fillMaxSize()) {
    // Top App Bar with haze effect
    TopAppBar(
      title = {
        Text(
          text = when (config.type) {
            MoreListType.RECOMMENDED -> strings.recommendedForYou
            MoreListType.NEW_RELEASES -> strings.newReleases
            MoreListType.CATEGORY -> config.categoryName ?: "Category"
          },
          fontSize = 18.sp,
          fontWeight = FontWeight.Medium
        )
      },
      navigationIcon = {
        IconButton(onClick = onBackClick) {
          Icon(
            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
            contentDescription = "Back"
          )
        }
      },
      modifier = Modifier.hazeEffect(
        state = hazeState,
        style = HazeDefaults.style(backgroundColor = MaterialTheme.colorScheme.surface),
      )
    )

    when {
      uiState.isLoading && uiState.dramas.isEmpty() -> {
        Box(
          modifier = Modifier.fillMaxSize(),
          contentAlignment = Alignment.Center
        ) {
          CircularProgressIndicator()
        }
      }

      uiState.error != null && uiState.dramas.isEmpty() -> {
        Box(
          modifier = Modifier.fillMaxSize(),
          contentAlignment = Alignment.Center
        ) {
          Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
          ) {
            Text(
              text = uiState.error,
              style = MaterialTheme.typography.bodyLarge,
              color = MaterialTheme.colorScheme.error
            )
            Button(onClick = onRetry) {
              Text("Retry")
            }
          }
        }
      }

      else -> {
        LazyColumn(
          state = listState,
          modifier = Modifier
            .fillMaxSize()
            .hazeSource(state = hazeState),
          contentPadding = PaddingValues(16.dp),
          verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
          items(
            items = uiState.dramas.chunked(2), // Group items in pairs for 2-column layout
            key = { dramasPair -> dramasPair.firstOrNull()?.id ?: 0 }
          ) { dramasPair ->
            Row(
              modifier = Modifier.fillMaxWidth(),
              horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
              dramasPair.forEach { drama ->
                DramaGridItem(
                  drama = drama,
                  onClick = { onDramaClick(drama) },
                  modifier = Modifier.weight(1f)
                )
              }
              // Fill empty space if odd number of items
              if (dramasPair.size == 1) {
                Spacer(modifier = Modifier.weight(1f))
              }
            }
          }

          // Loading more indicator
          if (uiState.isLoadingMore) {
            item {
              Box(
                modifier = Modifier
                  .fillMaxWidth()
                  .padding(16.dp),
                contentAlignment = Alignment.Center
              ) {
                CircularProgressIndicator()
              }
            }
          }

          // End of list indicator
          if (!uiState.hasMore && uiState.dramas.isNotEmpty()) {
            item {
              Box(
                modifier = Modifier
                  .fillMaxWidth()
                  .padding(16.dp),
                contentAlignment = Alignment.Center
              ) {
                Text(
                  text = "No more content",
                  style = MaterialTheme.typography.bodyMedium,
                  color = Color.Gray
                )
              }
            }
          }
        }
      }
    }
  }
}

@Composable
private fun DramaGridItem(
  drama: ShortPlay,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Card(
    modifier = modifier
      .fillMaxWidth()
      .clickable { onClick() },
    shape = RoundedCornerShape(12.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
    colors = CardDefaults.cardColors(
      containerColor = MaterialTheme.colorScheme.surface
    )
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .aspectRatio(3 / 4f),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier
          .padding(vertical = 12.dp, horizontal = 12.dp)
      ) {
        Text(
          text = drama.title,
          fontSize = 14.sp,
          lineHeight = 18.sp,
          fontWeight = FontWeight.SemiBold,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
          color = MaterialTheme.colorScheme.onSurface
        )

        Text(
          text = strings.episodesCount(drama.total),
          fontSize = 11.sp,
          lineHeight = 15.sp,
          color = MaterialTheme.colorScheme.onSurfaceVariant,
          modifier = Modifier.padding(top = 4.dp)
        )
      }
    }
  }
}
