package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.layout.Box
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.number.formatWithCommas
import com.example.shorts.ui.node.screen.wallet.WalletBalance

@Composable
fun WithdrawalNoReachTips(
  withdrawal: Withdrawal?,
  walletBalance: WalletBalance,
  modifier: Modifier = Modifier,
) {
  Box(modifier = modifier) {
    if (withdrawal == null) return
    if (withdrawal.amount <= walletBalance.amount) return

    val strings = LocalStrings.current
    val neededAmount = withdrawal.amount.subtract(walletBalance.amount)
    val minimumAmount = withdrawal.displayAmount
    val neededAmountText = "${withdrawal.currencySymbol}${neededAmount.formatWithCommas()}"

    Text(
      text = strings.withdrawalNoReachTips(
        minimumAmount,
        neededAmountText,
        MaterialTheme.colorScheme.secondary
      ),
      fontSize = 11.sp,
      lineHeight = 15.sp,
      color = Color.White.copy(alpha = 0.7f)
    )
  }
}