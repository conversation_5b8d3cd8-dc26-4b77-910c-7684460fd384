package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.WithdrawToWallet: ImageVector
  get() {
    if (_WithdrawToWallet != null) {
      return _WithdrawToWallet!!
    }
    _WithdrawToWallet = ImageVector.Builder(
      name = "WithdrawToWallet",
      defaultWidth = 50.dp,
      defaultHeight = 51.dp,
      viewportWidth = 50f,
      viewportHeight = 51f
    ).apply {
      path(
        fill = SolidColor(Color(0xFF595969)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(11.342f, 22.342f)
        lineTo(32.273f, 16.976f)
        curveTo(34.413f, 16.427f, 36.592f, 17.717f, 37.141f, 19.857f)
        curveTo(37.146f, 19.876f, 37.151f, 19.894f, 37.155f, 19.913f)
        lineTo(39.491f, 29.607f)
        curveTo(40.135f, 32.282f, 38.567f, 34.993f, 35.926f, 35.767f)
        lineTo(21.294f, 40.057f)
        curveTo(18.56f, 40.859f, 15.693f, 39.292f, 14.891f, 36.557f)
        curveTo(14.871f, 36.49f, 14.853f, 36.422f, 14.836f, 36.354f)
        lineTo(11.342f, 22.342f)
        lineTo(11.342f, 22.342f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(25.969f, 14.056f)
        lineToRelative(0f, -8.786f)
        lineToRelative(4.844f, 0f)
        lineToRelative(0f, 8.786f)
        lineToRelative(4.072f, 0f)
        lineToRelative(-6.299f, 5.991f)
        lineToRelative(-6.704f, -5.991f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f
      ) {
        moveTo(15.098f, 7.96f)
        curveTo(14.368f, 7.998f, 13.775f, 8.187f, 13.297f, 8.539f)
        curveTo(12.667f, 8.98f, 12.352f, 9.61f, 12.352f, 10.429f)
        curveTo(12.352f, 11.361f, 12.793f, 12.054f, 13.675f, 12.52f)
        lineTo(15.098f, 13.049f)
        lineTo(15.098f, 16.123f)
        curveTo(14.67f, 16.073f, 14.342f, 15.934f, 14.116f, 15.708f)
        curveTo(13.813f, 15.418f, 13.649f, 14.927f, 13.599f, 14.246f)
        lineTo(12.239f, 14.246f)
        curveTo(12.314f, 15.279f, 12.604f, 16.035f, 13.133f, 16.539f)
        curveTo(13.586f, 16.98f, 14.242f, 17.232f, 15.098f, 17.282f)
        lineTo(15.098f, 18.391f)
        lineTo(15.879f, 18.391f)
        lineTo(15.879f, 17.282f)
        curveTo(16.66f, 17.232f, 17.29f, 17.043f, 17.794f, 16.715f)
        curveTo(18.449f, 16.262f, 18.789f, 15.594f, 18.789f, 14.7f)
        curveTo(18.789f, 13.793f, 18.349f, 13.1f, 17.479f, 12.608f)
        curveTo(17.353f, 12.558f, 16.812f, 12.344f, 15.879f, 11.991f)
        lineTo(15.879f, 9.106f)
        curveTo(16.219f, 9.144f, 16.497f, 9.245f, 16.711f, 9.409f)
        curveTo(17.026f, 9.635f, 17.227f, 10.026f, 17.303f, 10.568f)
        lineTo(18.638f, 10.568f)
        curveTo(18.512f, 9.661f, 18.197f, 8.993f, 17.681f, 8.59f)
        curveTo(17.227f, 8.224f, 16.635f, 8.01f, 15.879f, 7.96f)
        lineTo(15.879f, 6.927f)
        lineTo(15.098f, 6.927f)
        lineTo(15.098f, 7.96f)
        close()
        moveTo(15.879f, 13.327f)
        curveTo(15.955f, 13.352f, 16.03f, 13.39f, 16.106f, 13.415f)
        curveTo(16.988f, 13.717f, 17.429f, 14.171f, 17.429f, 14.75f)
        curveTo(17.429f, 15.191f, 17.24f, 15.531f, 16.887f, 15.783f)
        curveTo(16.61f, 15.96f, 16.282f, 16.073f, 15.879f, 16.123f)
        lineTo(15.879f, 13.327f)
        close()
        moveTo(15.098f, 11.701f)
        curveTo(15.073f, 11.701f, 15.06f, 11.689f, 15.048f, 11.689f)
        curveTo(14.166f, 11.361f, 13.725f, 10.946f, 13.725f, 10.429f)
        curveTo(13.725f, 9.95f, 13.889f, 9.598f, 14.242f, 9.371f)
        curveTo(14.456f, 9.22f, 14.745f, 9.119f, 15.098f, 9.094f)
        lineTo(15.098f, 11.701f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(11.998f, 22.177f)
        lineTo(38.002f, 22.177f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 42.002f, 26.177f)
        lineTo(42.002f, 42.516f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 38.002f, 46.516f)
        lineTo(11.998f, 46.516f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 7.998f, 42.516f)
        lineTo(7.998f, 26.177f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 11.998f, 22.177f)
        close()
      }
      path(
        fill = SolidColor(Color(0xFF1D1D27)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(35.034f, 27.334f)
        lineTo(42.002f, 27.334f)
        lineTo(42.002f, 27.334f)
        lineTo(42.002f, 41.358f)
        lineTo(35.034f, 41.358f)
        curveTo(31.162f, 41.358f, 28.022f, 38.219f, 28.022f, 34.346f)
        curveTo(28.022f, 30.473f, 31.162f, 27.334f, 35.034f, 27.334f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(35.065f, 28.793f)
        lineTo(41.516f, 28.793f)
        lineTo(41.516f, 28.793f)
        lineTo(41.516f, 39.899f)
        lineTo(35.065f, 39.899f)
        curveTo(31.998f, 39.899f, 29.512f, 37.413f, 29.512f, 34.346f)
        curveTo(29.512f, 31.279f, 31.998f, 28.793f, 35.065f, 28.793f)
        close()
      }
      path(
        fill = SolidColor(Color(0xFF1D1D27)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(33.978f, 34.242f)
        moveToRelative(-2.692f, 0f)
        arcToRelative(2.692f, 2.692f, 0f, isMoreThanHalf = true, isPositiveArc = true, 5.385f, 0f)
        arcToRelative(2.692f, 2.692f, 0f, isMoreThanHalf = true, isPositiveArc = true, -5.385f, 0f)
      }
    }.build()

    return _WithdrawToWallet!!
  }

@Suppress("ObjectPropertyName")
private var _WithdrawToWallet: ImageVector? = null

@Preview
@Composable
private fun WithdrawToWalletPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.WithdrawToWallet, contentDescription = null)
  }
}
