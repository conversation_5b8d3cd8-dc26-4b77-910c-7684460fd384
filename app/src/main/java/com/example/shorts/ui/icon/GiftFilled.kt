package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.GiftFilled: ImageVector
  get() {
    if (_GiftFilled != null) {
      return _GiftFilled!!
    }
    _GiftFilled = ImageVector.Builder(
      name = "GiftFilled",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        fill = SolidColor(Color.Black),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(16.684f, 32.678f)
        lineTo(53.316f, 32.678f)
        lineTo(53.316f, 56f)
        curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
        lineTo(19.684f, 59f)
        curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
        lineTo(16.684f, 32.678f)
        lineTo(16.684f, 32.678f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFE42B70),
            1f to Color(0xFFCB0A52)
          ),
          start = Offset(16.684f, 44.799f),
          end = Offset(53.316f, 44.799f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(16.684f, 32.678f)
        lineTo(53.316f, 32.678f)
        lineTo(53.316f, 56f)
        curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
        lineTo(19.684f, 59f)
        curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
        lineTo(16.684f, 32.678f)
        lineTo(16.684f, 32.678f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(16.684f, 32.678f)
          lineTo(53.316f, 32.678f)
          lineTo(53.316f, 56f)
          curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
          lineTo(19.684f, 59f)
          curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
          lineTo(16.684f, 32.678f)
          lineTo(16.684f, 32.678f)
          close()
        }
      ) {
        path(
          fill = Brush.linearGradient(
            colorStops = arrayOf(
              0f to Color(0xFFF35B72),
              1f to Color(0xFFE23852)
            ),
            start = Offset(16.684f, 43.312f),
            end = Offset(35f, 43.312f)
          ),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(16.684f, 29.169f)
          lineTo(35f, 29.169f)
          lineTo(35f, 59f)
          lineTo(19.684f, 59f)
          curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
          lineTo(16.684f, 29.169f)
          lineTo(16.684f, 29.169f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(16.684f, 32.678f)
          lineTo(53.316f, 32.678f)
          lineTo(53.316f, 56f)
          curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
          lineTo(19.684f, 59f)
          curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
          lineTo(16.684f, 32.678f)
          lineTo(16.684f, 32.678f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFF6D35E)),
          strokeLineWidth = 1f
        ) {
          moveTo(29.536f, 31.269f)
          lineTo(40.465f, 31.269f)
          lineTo(40.465f, 46.276f)
          curveTo(40.465f, 46.829f, 40.017f, 47.276f, 39.465f, 47.276f)
          curveTo(39.243f, 47.276f, 39.028f, 47.203f, 38.853f, 47.067f)
          lineTo(35f, 44.085f)
          lineTo(35f, 44.085f)
          lineTo(31.148f, 47.067f)
          curveTo(30.711f, 47.405f, 30.083f, 47.325f, 29.745f, 46.889f)
          curveTo(29.61f, 46.713f, 29.536f, 46.498f, 29.536f, 46.276f)
          lineTo(29.536f, 31.269f)
          lineTo(29.536f, 31.269f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(29.536f, 31.269f)
          lineTo(40.465f, 31.269f)
          lineTo(40.465f, 46.276f)
          curveTo(40.465f, 46.829f, 40.017f, 47.276f, 39.465f, 47.276f)
          curveTo(39.243f, 47.276f, 39.028f, 47.203f, 38.853f, 47.067f)
          lineTo(35f, 44.085f)
          lineTo(35f, 44.085f)
          lineTo(31.148f, 47.067f)
          curveTo(30.711f, 47.405f, 30.083f, 47.325f, 29.745f, 46.889f)
          curveTo(29.61f, 46.713f, 29.536f, 46.498f, 29.536f, 46.276f)
          lineTo(29.536f, 31.269f)
          lineTo(29.536f, 31.269f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFE59B53)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(35.245f, 34.702f)
          lineToRelative(-0.245f, 11.049f)
          lineToRelative(5.464f, 2.563f)
          lineToRelative(1.453f, -17.046f)
          close()
        }
      }
      path(
        stroke = SolidColor(Color(0xFFFFB94E)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(50.4f, 28.832f)
        lineTo(58.093f, 28.832f)
      }
      path(
        stroke = SolidColor(Color(0xFFFFB94E)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(54.247f, 24.985f)
        lineTo(54.247f, 32.678f)
      }
      path(
        stroke = SolidColor(Color(0xFFFFB94E)),
        strokeLineWidth = 1f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(50.943f, 22.699f)
        lineTo(52.559f, 22.699f)
      }
      path(
        stroke = SolidColor(Color(0xFFFFB94E)),
        strokeLineWidth = 1f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(51.751f, 22.106f)
        lineTo(51.751f, 23.292f)
      }
      path(
        fill = SolidColor(Color(0xFFFFB94E)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(42.359f, 30.605f)
        moveToRelative(-1f, 0f)
        arcToRelative(1f, 1f, 0f, isMoreThanHalf = true, isPositiveArc = true, 2f, 0f)
        arcToRelative(1f, 1f, 0f, isMoreThanHalf = true, isPositiveArc = true, -2f, 0f)
      }
      path(
        fill = SolidColor(Color(0xFFF6D35E)),
        strokeLineWidth = 1f
      ) {
        moveTo(9.948f, 15.295f)
        lineTo(9.902f, 15.33f)
        curveTo(7.064f, 17.587f, 6.987f, 20.949f, 9.119f, 23.647f)
        curveTo(11.06f, 26.101f, 14.358f, 27.419f, 17.236f, 26.54f)
        curveTo(21.497f, 25.24f, 23.329f, 24.126f, 24.619f, 22.336f)
        curveTo(25.893f, 20.567f, 25.513f, 19.052f, 23.846f, 16.952f)
        curveTo(20.769f, 13.072f, 15.419f, 11.236f, 9.948f, 15.295f)
        close()
        moveTo(21.221f, 18.914f)
        lineTo(21.567f, 19.359f)
        curveTo(22.119f, 20.091f, 22.118f, 20.243f, 21.917f, 20.521f)
        lineTo(21.749f, 20.741f)
        curveTo(20.934f, 21.745f, 19.646f, 22.46f, 16.27f, 23.491f)
        curveTo(14.837f, 23.927f, 12.911f, 23.158f, 11.748f, 21.688f)
        lineTo(11.618f, 21.516f)
        curveTo(10.665f, 20.205f, 10.7f, 18.923f, 11.83f, 17.922f)
        lineTo(11.962f, 17.81f)
        lineTo(11.939f, 17.827f)
        curveTo(15.658f, 15.07f, 19.111f, 16.255f, 21.221f, 18.914f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(9.05f, 17.007f)
          lineTo(9.014f, 17.051f)
          curveTo(6.807f, 19.928f, 7.545f, 23.209f, 10.266f, 25.311f)
          curveTo(12.744f, 27.223f, 16.262f, 27.704f, 18.842f, 26.154f)
          curveTo(22.662f, 23.862f, 24.17f, 22.338f, 24.989f, 20.289f)
          curveTo(25.797f, 18.264f, 25.062f, 16.887f, 22.936f, 15.252f)
          curveTo(19.012f, 12.232f, 13.377f, 11.745f, 9.05f, 17.007f)
          close()
          moveTo(20.864f, 17.791f)
          lineTo(21.308f, 18.139f)
          curveTo(22.02f, 18.715f, 22.055f, 18.864f, 21.928f, 19.182f)
          lineTo(21.818f, 19.435f)
          curveTo(21.27f, 20.607f, 20.193f, 21.612f, 17.167f, 23.43f)
          curveTo(15.883f, 24.2f, 13.827f, 23.919f, 12.343f, 22.774f)
          lineTo(12.175f, 22.639f)
          curveTo(10.933f, 21.597f, 10.657f, 20.344f, 11.512f, 19.1f)
          lineTo(11.613f, 18.96f)
          lineTo(11.595f, 18.982f)
          curveTo(14.536f, 15.407f, 18.173f, 15.721f, 20.864f, 17.791f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFDFAD42)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(11.224f, 24.613f)
          curveTo(12.149f, 23.376f, 21.636f, 18.603f, 22.815f, 18.127f)
          curveTo(23.601f, 17.809f, 23.992f, 18.776f, 23.988f, 21.029f)
          curveTo(18.141f, 25.223f, 14.721f, 26.919f, 13.728f, 26.116f)
          curveTo(12.238f, 24.912f, 10.299f, 25.85f, 11.224f, 24.613f)
          close()
        }
      }
      path(
        fill = SolidColor(Color(0xFFF6D35E)),
        strokeLineWidth = 1f
      ) {
        moveTo(30.897f, 6.847f)
        curveTo(34.506f, 6.499f, 36.898f, 8.864f, 37.238f, 12.286f)
        curveTo(37.546f, 15.4f, 36.09f, 18.639f, 33.408f, 20.006f)
        curveTo(29.441f, 22.031f, 27.349f, 22.501f, 25.177f, 22.11f)
        curveTo(23.032f, 21.723f, 22.253f, 20.369f, 21.993f, 17.701f)
        curveTo(21.519f, 12.858f, 23.997f, 7.908f, 30.495f, 6.902f)
        lineTo(30.897f, 6.847f)
        close()
        moveTo(25.245f, 17.288f)
        lineTo(25.305f, 17.849f)
        curveTo(25.409f, 18.706f, 25.511f, 18.852f, 25.803f, 18.916f)
        lineTo(25.86f, 18.928f)
        curveTo(27.224f, 19.173f, 28.616f, 18.86f, 31.986f, 17.141f)
        curveTo(33.319f, 16.46f, 34.171f, 14.568f, 33.986f, 12.703f)
        lineTo(33.96f, 12.489f)
        curveTo(33.735f, 10.884f, 32.819f, 9.986f, 31.31f, 10.051f)
        lineTo(31.133f, 10.064f)
        lineTo(31.167f, 10.059f)
        curveTo(26.576f, 10.659f, 24.915f, 13.91f, 25.245f, 17.288f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(21.829f, 11.874f)
          curveTo(19.622f, 14.75f, 20.36f, 18.031f, 23.081f, 20.133f)
          curveTo(25.559f, 22.045f, 29.077f, 22.526f, 31.658f, 20.977f)
          curveTo(35.477f, 18.685f, 36.985f, 17.16f, 37.804f, 15.111f)
          curveTo(38.613f, 13.087f, 37.877f, 11.709f, 35.752f, 10.074f)
          curveTo(31.896f, 7.106f, 26.386f, 6.585f, 22.089f, 11.561f)
          lineTo(21.829f, 11.874f)
          close()
          moveTo(33.679f, 12.613f)
          lineTo(34.123f, 12.961f)
          curveTo(34.794f, 13.504f, 34.864f, 13.667f, 34.764f, 13.949f)
          lineTo(34.743f, 14.004f)
          curveTo(34.229f, 15.29f, 33.225f, 16.305f, 29.982f, 18.252f)
          curveTo(28.698f, 19.022f, 26.642f, 18.742f, 25.158f, 17.596f)
          lineTo(24.99f, 17.461f)
          curveTo(23.749f, 16.419f, 23.472f, 15.167f, 24.327f, 13.922f)
          lineTo(24.432f, 13.778f)
          lineTo(24.41f, 13.804f)
          curveTo(27.351f, 10.229f, 30.989f, 10.543f, 33.679f, 12.613f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFDF8F42)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(22.673f, 20.072f)
          curveTo(23.145f, 19.441f, 27.248f, 18.816f, 30.614f, 16.864f)
          curveTo(33.849f, 14.988f, 36.349f, 11.775f, 36.927f, 11.541f)
          curveTo(38.106f, 11.065f, 39.957f, 13.712f, 36.481f, 15.803f)
          curveTo(33.005f, 17.893f, 27.849f, 21.779f, 26.359f, 20.574f)
          curveTo(24.869f, 19.37f, 21.748f, 21.309f, 22.673f, 20.072f)
          close()
        }
      }
      path(
        fill = SolidColor(Color.Black),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(4.964f, 27.624f)
        lineTo(42.978f, 12.266f)
        arcTo(2f, 2f, 122.199f, isMoreThanHalf = false, isPositiveArc = true, 45.582f, 13.371f)
        lineTo(48.602f, 20.845f)
        arcTo(2f, 2f, 132.515f, isMoreThanHalf = false, isPositiveArc = true, 47.496f, 23.448f)
        lineTo(9.482f, 38.807f)
        arcTo(2f, 2f, 99.217f, isMoreThanHalf = false, isPositiveArc = true, 6.878f, 37.702f)
        lineTo(3.859f, 30.228f)
        arcTo(2f, 2f, 67.5f, isMoreThanHalf = false, isPositiveArc = true, 4.964f, 27.624f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFE42B70),
            1f to Color(0xFFCB0A52)
          ),
          start = Offset(3.713f, 24.477f),
          end = Offset(48.747f, 24.477f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(4.964f, 27.624f)
        lineTo(42.978f, 12.266f)
        arcTo(2f, 2f, 122.199f, isMoreThanHalf = false, isPositiveArc = true, 45.582f, 13.371f)
        lineTo(48.602f, 20.845f)
        arcTo(2f, 2f, 132.515f, isMoreThanHalf = false, isPositiveArc = true, 47.496f, 23.448f)
        lineTo(9.482f, 38.807f)
        arcTo(2f, 2f, 99.217f, isMoreThanHalf = false, isPositiveArc = true, 6.878f, 37.702f)
        lineTo(3.859f, 30.228f)
        arcTo(2f, 2f, 67.5f, isMoreThanHalf = false, isPositiveArc = true, 4.964f, 27.624f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(4.964f, 27.624f)
          lineTo(42.978f, 12.266f)
          arcTo(2f, 2f, 122.199f, isMoreThanHalf = false, isPositiveArc = true, 45.582f, 13.371f)
          lineTo(48.602f, 20.845f)
          arcTo(2f, 2f, 132.515f, isMoreThanHalf = false, isPositiveArc = true, 47.496f, 23.448f)
          lineTo(9.482f, 38.807f)
          arcTo(2f, 2f, 99.217f, isMoreThanHalf = false, isPositiveArc = true, 6.878f, 37.702f)
          lineTo(3.859f, 30.228f)
          arcTo(2f, 2f, 67.5f, isMoreThanHalf = false, isPositiveArc = true, 4.964f, 27.624f)
          close()
        }
      ) {
        path(
          fill = Brush.linearGradient(
            colorStops = arrayOf(
              0f to Color(0x99FB9A5A),
              1f to Color(0xFFF28430)
            ),
            start = Offset(2.381f, 26.592f),
            end = Offset(29.938f, 26.592f)
          ),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(35.704f, 13.88f)
          lineToRelative(-4.955f, 16.334f)
          lineToRelative(-25.938f, 10.48f)
          lineToRelative(-4.978f, -12.321f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(4.964f, 27.624f)
          lineTo(42.978f, 12.266f)
          arcTo(2f, 2f, 122.199f, isMoreThanHalf = false, isPositiveArc = true, 45.582f, 13.371f)
          lineTo(48.602f, 20.845f)
          arcTo(2f, 2f, 132.515f, isMoreThanHalf = false, isPositiveArc = true, 47.496f, 23.448f)
          lineTo(9.482f, 38.807f)
          arcTo(2f, 2f, 99.217f, isMoreThanHalf = false, isPositiveArc = true, 6.878f, 37.702f)
          lineTo(3.859f, 30.228f)
          arcTo(2f, 2f, 67.5f, isMoreThanHalf = false, isPositiveArc = true, 4.964f, 27.624f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFF6D35E)),
          strokeLineWidth = 1f
        ) {
          moveTo(18.229f, 22.265f)
          lineToRelative(11.434f, -4.619f)
          lineToRelative(4.63f, 11.459f)
          lineToRelative(-11.434f, 4.619f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(4.964f, 27.624f)
          lineTo(42.978f, 12.266f)
          arcTo(2f, 2f, 122.199f, isMoreThanHalf = false, isPositiveArc = true, 45.582f, 13.371f)
          lineTo(48.602f, 20.845f)
          arcTo(2f, 2f, 132.515f, isMoreThanHalf = false, isPositiveArc = true, 47.496f, 23.448f)
          lineTo(9.482f, 38.807f)
          arcTo(2f, 2f, 99.217f, isMoreThanHalf = false, isPositiveArc = true, 6.878f, 37.702f)
          lineTo(3.859f, 30.228f)
          arcTo(2f, 2f, 67.5f, isMoreThanHalf = false, isPositiveArc = true, 4.964f, 27.624f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFE59B53)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(23.971f, 19.945f)
          lineToRelative(5.691f, -2.299f)
          lineToRelative(4.63f, 11.459f)
          lineToRelative(-5.691f, 2.299f)
          close()
        }
      }
    }.build()

    return _GiftFilled!!
  }

@Suppress("ObjectPropertyName")
private var _GiftFilled: ImageVector? = null

@Preview
@Composable
private fun GiftFilledPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.GiftFilled, contentDescription = null)
  }
}
