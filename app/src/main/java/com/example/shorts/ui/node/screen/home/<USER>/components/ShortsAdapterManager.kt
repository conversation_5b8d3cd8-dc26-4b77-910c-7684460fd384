package com.example.shorts.ui.node.screen.home.shorts.components

import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.widget.ViewPager2
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.kermit.debugLog
import java.util.concurrent.ConcurrentHashMap

/**
 * 单例管理器，用于缓存和管理ViewPager2及其Adapter
 * 解决tab切换时的内存泄漏和多视频同时播放问题
 */
object ShortsAdapterManager {

    private val cachedViewPagers = ConcurrentHashMap<String, ViewPager2>()
    private val cachedAdapters = ConcurrentHashMap<String, ShortsViewPagerAdapter>()

    /**
     * 获取或创建ViewPager2实例
     */
    fun getOrCreateViewPager(
        activity: FragmentActivity,
        cacheKey: String = "default"
    ): ViewPager2 {
        return cachedViewPagers.getOrPut(cacheKey) {
            debugLog(tag = "ShortsAdapterManager") { "Creating new ViewPager2 for key: $cacheKey" }
            ViewPager2(activity).apply {
                orientation = ViewPager2.ORIENTATION_VERTICAL
                offscreenPageLimit = 1
            }
        }
    }

    /**
     * 获取或创建Adapter实例
     */
    fun getOrCreateAdapter(
        activity: FragmentActivity,
        shortPlays: List<ShortPlay>,
        onPageSelected: (Int) -> Unit,
        cacheKey: String = "default"
    ): ShortsViewPagerAdapter {
        return cachedAdapters.getOrPut(cacheKey) {
            debugLog(tag = "ShortsAdapterManager") { "Creating new ShortsViewPagerAdapter for key: $cacheKey" }
            ShortsViewPagerAdapter(activity, shortPlays, onPageSelected)
        }.also { adapter ->
            // 更新数据
            adapter.setShortPlays(shortPlays)
            // 更新回调
            adapter.updatePageSelectedCallback(onPageSelected)
        }
    }

    fun getDefaultAdapter(): ShortsViewPagerAdapter? {
        return cachedAdapters["default"]
    }

    /**
     * 暂停指定key的adapter（暂停视频播放）
     */
    fun pauseAdapter(cacheKey: String = "default") {
        cachedAdapters[cacheKey]?.let { adapter ->
            debugLog(tag = "ShortsAdapterManager") { "Pausing adapter for key: $cacheKey" }
            adapter.pauseAllVideos()
        }
    }

    /**
     * 恢复指定key的adapter（恢复视频播放）
     */
    fun resumeAdapter(cacheKey: String = "default") {
        cachedAdapters[cacheKey]?.let { adapter ->
            debugLog(tag = "ShortsAdapterManager") { "Resuming adapter for key: $cacheKey" }
//            adapter.resumeCurrentVideo()
            adapter.resumeAllVideos()
            adapter.setMutedCurrentVideo(false)
        }
    }

    /**
     * 获取当前播放位置
     */
    fun getCurrentPosition(cacheKey: String = "default"): Int {
        return cachedAdapters[cacheKey]?.getCurrentPosition() ?: 0
    }

    /**
     * 设置播放位置
     */
    fun setCurrentPosition(position: Int, cacheKey: String = "default") {
        val viewPager = cachedViewPagers[cacheKey]
        if (viewPager != null) {
            viewPager.setCurrentItem(position, false)
        }
    }

    /**
     * 清理指定key的缓存
     */
    fun clearCache(cacheKey: String = "default") {
        debugLog(tag = "ShortsAdapterManager") { "Clearing cache for key: $cacheKey" }

        cachedAdapters[cacheKey]?.let { adapter ->
            adapter.destroy()
            cachedAdapters.remove(cacheKey)
        }

        cachedViewPagers.remove(cacheKey)
    }

    /**
     * 清理所有缓存
     */
    fun clearAllCache() {
        debugLog(tag = "ShortsAdapterManager") { "Clearing all cache" }

        cachedAdapters.values.forEach { adapter ->
            adapter.destroy()
        }
        cachedAdapters.clear()
        cachedViewPagers.clear()
    }

    /**
     * 检查是否有缓存的adapter
     */
    fun hasCache(cacheKey: String = "default"): Boolean {
        return cachedAdapters.containsKey(cacheKey) && cachedViewPagers.containsKey(cacheKey)
    }
}