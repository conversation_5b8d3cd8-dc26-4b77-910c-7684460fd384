package com.example.shorts.ui.node.screen.home.tasks

import com.example.shorts.foundation.checkin.CheckInGridItem
import com.example.shorts.foundation.checkin.CheckInRepository
import com.example.shorts.foundation.checkin.CheckInResult
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.mmkv.WalletKvStore
import com.example.shorts.foundation.mvi_ui_model.UiModel
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.VideoIcon
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class CheckInUiModel(
  private val checkInRepository: CheckInRepository,
  private val walletKvStore: WalletKvStore,
) : UiModel<CheckInUiState, Nothing>(CheckInUiState()) {

  companion object {
    const val TAG = "CheckInUiModel"
  }

  init {
    onLoad()
  }

  fun onLoad() = intent {
    debugLog(tag = TAG) { "onLoad" }
    loadCheckInState()
  }

  private fun loadCheckInState() = intent {
    val checkInState = checkInRepository.getCheckInState()
    val rewards = checkInRepository.getCheckInRewards()

    // 构建UI项目列表
    val checkInItems = mutableListOf<CheckInGridItem>()

    // 添加7天签到项目
    rewards.forEach { reward ->
      val isChecked = reward.day <= checkInState.consecutiveDays
      val isToday = reward.day == checkInState.currentDay

      checkInItems.add(
        CheckInGridItem.CheckInDay(
          day = reward.day,
          reward = reward,
          isChecked = isChecked,
          isToday = isToday
        )
      )
    }

    // 添加外部链接项目（游戏手柄）
    checkInItems.add(
      CheckInGridItem.ExternalLink(
        icon = ValkyrieIcons.VideoIcon,
        onClick = { /* 外部链接点击，暂时留空 */ }
      )
    )

    reduce {
      state.copy(
        checkInItems = checkInItems,
        canSignIn = !checkInState.todayCheckedIn,
        todayCheckedIn = checkInState.todayCheckedIn,
        currentDay = checkInState.currentDay,
      )
    }
  }

  fun onSignInClick() = intent {
    if (!state.canSignIn) return@intent

    val result = checkInRepository.checkIn()
    when (result) {
      is CheckInResult.Success -> {
        debugLog(tag = TAG) { "CheckIn success: day=${result.day}, reward=${result.reward.reward.amount}" }
        // 刷新状态
        loadCheckInState()

        // update wallet balance
        walletKvStore.amount.value += result.reward.reward.amount.toDouble()
      }

      CheckInResult.AlreadyCheckedIn -> {
        debugLog(tag = TAG) { "Already checked in today" }
      }
    }
  }
}