package com.example.shorts.ui.node.screen.home.shorts

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.widget.ViewPager2
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.ui.node.screen.home.shorts.components.ShortsViewPagerAdapter
import com.example.shorts.ui.node.screen.home.shorts.components.ShortsAdapterManager
import com.example.shorts.ui.node.screen.home.shorts.model.ShortsUiModel
import com.example.shorts.ui.node.screen.home.shorts.model.ShortsUiState
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun ShortsScreen(
  modifier: Modifier = Modifier,
  uiModel: ShortsUiModel = koinInject()
) {
  val uiState by uiModel.collectAsState()
  val context = LocalContext.current
  val activity = context as? FragmentActivity

  // 在组件被销毁时清理缓存（可选，用于调试或内存紧张时）
  // DisposableEffect(Unit) {
  //   onDispose {
  //     // 仅在必要时清理所有缓存，通常不需要
  //     // ShortsAdapterManager.clearAllCache()
  //   }
  // }

  if (activity == null) {
    Box(
      modifier = modifier.fillMaxSize(),
      contentAlignment = Alignment.Center
    ) {
      Text(
        text = "需要在FragmentActivity中使用",
        style = MaterialTheme.typography.bodyLarge
      )
    }
    return
  }

  when {
    uiState.isLoading && uiState.shortPlays.isEmpty() -> {
      debugLog(tag = "ShortsScreen") { "Loading" }
      Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        CircularProgressIndicator()
      }
    }

    uiState.error != null && uiState.shortPlays.isEmpty() -> {
      Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        Column(
          horizontalAlignment = Alignment.CenterHorizontally,
          verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
          Text(
            text = uiState.error ?: "",
            style = MaterialTheme.typography.bodyLarge
          )
          Button(
            onClick = { uiModel.loadShortPlays() }
          ) {
            Text("重试")
          }
        }
      }
    }

    uiState.shortPlays.isNotEmpty() -> {
      // 使用DisposableEffect管理生命周期
      DisposableEffect(activity) {
        // 恢复视频播放
        ShortsAdapterManager.resumeAdapter()

        onDispose {
          // 暂停视频播放，但保留缓存
          ShortsAdapterManager.pauseAdapter()
        }
      }

      // 创建稳定的onPageSelected回调引用
      val onPageSelected = remember(uiModel) {
        { position: Int ->
          // 预加载下一个视频
          if (position + 1 < uiState.shortPlays.size) {
            // 预加载逻辑
          }

          // 当接近末尾时加载更多
          if (position >= uiState.shortPlays.size - 3 && uiState.hasMore && !uiState.isLoading) {
            uiModel.loadMore()
          }
        }
      }

      // 缓存ViewPager2和Adapter
      val cachedViewPager = remember(activity) {
        ShortsAdapterManager.getOrCreateViewPager(activity)
      }

      // 将adapter创建移出update block
      val adapter = remember(activity) {
        ShortsAdapterManager.getOrCreateAdapter(
          activity = activity,
          shortPlays = uiState.shortPlays,
          onPageSelected = onPageSelected
        )
      }

      // 使用LaunchedEffect处理数据同步
      LaunchedEffect(uiState.shortPlays) {
        if (uiState.shortPlays.isNotEmpty()) {
          // 同步更新adapter数据
          adapter.setShortPlays(uiState.shortPlays)
          adapter.updatePageSelectedCallback(onPageSelected)
        }
      }

      AndroidView(
        factory = { cachedViewPager },
        modifier = modifier.fillMaxSize(),
        onRelease = {
          // 不在这里销毁缓存，而是暂停播放
          ShortsAdapterManager.pauseAdapter()
        }
      ) { viewPager ->
        // update block只做轻量级操作
        if (viewPager.adapter !== adapter) {
          viewPager.adapter = adapter
          // 恢复到之前的播放位置
          val lastPosition = ShortsAdapterManager.getCurrentPosition()
          if (lastPosition < uiState.shortPlays.size) {
            viewPager.setCurrentItem(lastPosition, false)
          }
        }
      }
    }
  }
}

