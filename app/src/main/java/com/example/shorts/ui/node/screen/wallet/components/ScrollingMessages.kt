package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.foundation.android.brighten
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.ui.node.screen.wallet.ScrollingMessage
import com.example.shorts.ui.theme.AppTheme
import com.sebaslogen.resaca.koin.koinViewModelScoped
import kotlinx.coroutines.delay
import org.orbitmvi.orbit.compose.collectAsState
import java.math.BigDecimal
import kotlin.math.roundToInt

@Composable
fun ScrollingMessages(
  modifier: Modifier = Modifier
) {
  val uiModel: ScrollingMessagesUiModel = koinViewModelScoped()
  val uiState by uiModel.collectAsState()

  ScrollingMessages(
    messages = uiState.messages,
    modifier = modifier
  )
}

/**
 * 滚动弹幕组件
 */
@Composable
fun ScrollingMessages(
  messages: List<ScrollingMessage>,
  modifier: Modifier = Modifier
) {
  if (messages.isEmpty()) return

  val (messages1, messages2) = remember(messages) {
    messages.chunked(messages.size / 2)
  }

  Column(
    modifier = modifier
      .fillMaxWidth(),
    verticalArrangement = Arrangement.spacedBy(8.dp)
  ) {
    // 第一行弹幕 - 从右到左
    ScrollingMessageRow(
      messages = messages1,
      direction = ScrollDirection.RightToLeft,
      delay = 0
    )

    // 第二行弹幕 - 从右到左，有延迟
    ScrollingMessageRow(
      messages = messages2,
      direction = ScrollDirection.RightToLeft,
      delay = 2000
    )
  }
}

/**
 * 滚动方向枚举
 */
private enum class ScrollDirection {
  RightToLeft, LeftToRight
}

/**
 * 单行滚动弹幕
 */
@Composable
private fun ScrollingMessageRow(
  messages: List<ScrollingMessage>,
  direction: ScrollDirection,
  delay: Int,
  modifier: Modifier = Modifier
) {
  if (messages.isEmpty()) return

  val screenWidth = LocalWindowInfo.current.containerSize.width
  val screenWidthF = screenWidth.toFloat()

  // 动画状态
  val infiniteTransition = rememberInfiniteTransition(label = "scrolling_transition")

  var startAnimation by remember { mutableStateOf(false) }
  var currentMessageIndex by remember { mutableStateOf(0) }

  // 延迟启动动画
  LaunchedEffect(Unit) {
    delay(delay.toLong())
    startAnimation = true
  }

  val animatedOffset by infiniteTransition.animateFloat(
    initialValue = screenWidthF,
    targetValue = if (startAnimation) {
      when (direction) {
        ScrollDirection.RightToLeft -> -screenWidthF
        ScrollDirection.LeftToRight -> screenWidthF
      }
    } else {
      screenWidthF
    },
    animationSpec = infiniteRepeatable(
      animation = tween(
        durationMillis = 7_000,
        easing = LinearEasing
      ),
      repeatMode = RepeatMode.Restart
    ),
    label = "scrolling_offset"
  )

  // 监听动画完成，切换到下一条消息
  LaunchedEffect(animatedOffset) {
    if (startAnimation && animatedOffset <= -screenWidthF + 10) {
      currentMessageIndex = (currentMessageIndex + 1) % messages.size
    }
  }

  Box(
    modifier = modifier
  ) {
    ScrollingMessageItem(
      message = messages[currentMessageIndex],
      modifier = Modifier.offset {
        IntOffset(
          x = animatedOffset.roundToInt(),
          y = 0
        )
      }
    )
  }
}

/**
 * 单个滚动消息项
 */
@Composable
private fun ScrollingMessageItem(
  message: ScrollingMessage,
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier
      .clip(CircleShape)
      .border(1.dp, MaterialTheme.colorScheme.secondary, CircleShape)
      .background(MaterialTheme.colorScheme.secondary.copy(.2f))
      .padding(horizontal = 12.dp, vertical = 4.dp)
  ) {
    Row(
      verticalAlignment = Alignment.CenterVertically,
    ) {
      Text(
        text = message.getDisplayText(LocalStrings.current, MaterialTheme.colorScheme.primary.brighten()),
        fontSize = 11.sp,
        fontWeight = FontWeight.Medium,
        maxLines = 2,
      )
    }
  }
}

@Preview
@Composable
fun ScrollingMessagesPreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .background(MaterialTheme.colorScheme.background)
    ) {
      ScrollingMessages(
        messages = listOf(
          ScrollingMessage("1", "Alex", Withdrawal(BigDecimal("10000.00"), "$")),
          ScrollingMessage("2", "Sarah", Withdrawal(BigDecimal("12000.00"), "$")),
          ScrollingMessage("3", "Mike", Withdrawal(BigDecimal("8500.00"), "$")),
          ScrollingMessage("4", "Emma", Withdrawal(BigDecimal("15000.00"), "$")),
          ScrollingMessage("5", "John", Withdrawal(BigDecimal("7200.00"), "$")),
          ScrollingMessage("6", "Lisa", Withdrawal(BigDecimal("9800.00"), "$"))
        )
      )
    }
  }
}
