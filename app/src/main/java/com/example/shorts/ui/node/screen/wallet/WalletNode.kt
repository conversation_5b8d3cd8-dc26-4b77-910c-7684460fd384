package com.example.shorts.ui.node.screen.wallet

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientButtonSize
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.node.dialog.edit_wallet_address.EditWalletAddressDialogNode
import com.example.shorts.ui.node.screen.wallet.components.*
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.push
import dev.chrisbanes.haze.HazeDefaults
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState
import kotlinx.parcelize.Parcelize
import org.orbitmvi.orbit.compose.collectAsState

@Parcelize
class WalletNode : ScreenNode("wallet") {
  @Composable
  override fun Content(navigator: Navigator) {
    val uiModel: WalletUiModel = koinUiModel()
    val uiState by uiModel.collectAsState()
    val context = LocalContext.current

    WalletContent(
      uiState = uiState,
      onBackClick = navigator::pop,
      onWithdrawalSelect = uiModel::selectWithdrawal,
      onCashOutClick = uiModel::onCashOutClick,
      onSetWalletAddress = { navigator.push(EditWalletAddressDialogNode()) }
    )
  }
}

/**
 * 钱包页面内容
 */
@Composable
private fun WalletContent(
  uiState: WalletUiState,
  onBackClick: () -> Unit,
  onWithdrawalSelect: (Withdrawal) -> Unit,
  onCashOutClick: () -> Unit,
  onSetWalletAddress: () -> Unit
) {
  val strings = LocalStrings.current
  val hazeState = rememberHazeState()

  Scaffold(
    topBar = {
      WalletTopBar(
        title = strings.myWallet,
        onBackClick = onBackClick,
        onSetWalletAddress = onSetWalletAddress,
        modifier = Modifier.hazeEffect(
          state = hazeState,
          style = HazeDefaults.style(backgroundColor = MaterialTheme.colorScheme.surface),
        )
      )
    },
    bottomBar = {
      BottomAppBar(
        containerColor = Color.Transparent,
        modifier = Modifier.hazeEffect(
          state = hazeState,
        )
      ) {
        GradientButton(
          text = if (uiState.isLoading) strings.processing else strings.cashOut,
          onClick = onCashOutClick,
          modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
          size = GradientButtonSize.Large,
          gradient = GradientColors.Purple,
          enabled = !uiState.isLoading && uiState.selectedWithdrawal != null && uiState.balance.amount >= uiState.selectedWithdrawal.amount
        )
      }
    }
  ) { paddingValues ->
    Column(
      modifier = Modifier
        .fillMaxSize()
        .padding(paddingValues)
        .verticalScroll(rememberScrollState())
        .hazeSource(state = hazeState),
      verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      Spacer(modifier = Modifier.height(8.dp))

      // 钱包余额卡片
      WalletBalanceCard(balance = uiState.balance, modifier = Modifier.padding(horizontal = 16.dp))

      Spacer(modifier = Modifier.height(8.dp))

      // 提现金额选择
      WithdrawAmountSelector(
        withdrawals = uiState.withdrawals,
        selectedWithdrawal = uiState.selectedWithdrawal,
        onSelect = onWithdrawalSelect,
        modifier = Modifier.padding(horizontal = 16.dp)
      )

      WithdrawalNoReachTips(
        withdrawal = uiState.selectedWithdrawal,
        walletBalance = uiState.balance,
        modifier = Modifier
          .padding(horizontal = 16.dp)
          .animateContentSize()
      )

      // 滚动弹幕
      ScrollingMessages()

      // 重要提示
      ImportantNotice(modifier = Modifier.padding(horizontal = 16.dp))

      Spacer(modifier = Modifier.height(4.dp))
    }
  }
}

