package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.NoRecordPlaceholder: ImageVector
  get() {
    if (_NoRecordPlaceholder != null) {
      return _NoRecordPlaceholder!!
    }
    _NoRecordPlaceholder = ImageVector.Builder(
      name = "NoRecordPlaceholder",
      defaultWidth = 260.dp,
      defaultHeight = 260.dp,
      viewportWidth = 260f,
      viewportHeight = 260f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(75.5f, 54f)
        lineTo(182.5f, 54f)
        arcTo(19.5f, 19.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 202f, 73.5f)
        lineTo(202f, 202.5f)
        arcTo(19.5f, 19.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 182.5f, 222f)
        lineTo(75.5f, 222f)
        arcTo(19.5f, 19.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 56f, 202.5f)
        lineTo(56f, 73.5f)
        arcTo(19.5f, 19.5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 75.5f, 54f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(24f, 70.5f)
        arcToRelative(6f, 6.5f, 0f, isMoreThanHalf = true, isPositiveArc = false, 12f, 0f)
        arcToRelative(6f, 6.5f, 0f, isMoreThanHalf = true, isPositiveArc = false, -12f, 0f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(125f, 81f)
        lineTo(210f, 81f)
        arcTo(13f, 13f, 0f, isMoreThanHalf = false, isPositiveArc = true, 223f, 94f)
        lineTo(223f, 174f)
        arcTo(13f, 13f, 0f, isMoreThanHalf = false, isPositiveArc = true, 210f, 187f)
        lineTo(125f, 187f)
        arcTo(13f, 13f, 0f, isMoreThanHalf = false, isPositiveArc = true, 112f, 174f)
        lineTo(112f, 94f)
        arcTo(13f, 13f, 0f, isMoreThanHalf = false, isPositiveArc = true, 125f, 81f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(156.6f, 137.9f)
        lineTo(136.15f, 150.14f)
        curveTo(134.22f, 151.29f, 131.73f, 150.67f, 130.58f, 148.74f)
        curveTo(130.2f, 148.11f, 130f, 147.39f, 130f, 146.65f)
        lineTo(130f, 122.17f)
        curveTo(130f, 119.92f, 131.82f, 118.1f, 134.06f, 118.1f)
        curveTo(134.8f, 118.1f, 135.52f, 118.3f, 136.15f, 118.68f)
        lineTo(156.6f, 130.92f)
        curveTo(158.53f, 132.08f, 159.16f, 134.57f, 158f, 136.5f)
        curveTo(157.66f, 137.07f, 157.18f, 137.55f, 156.6f, 137.9f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        strokeLineCap = StrokeCap.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(170f, 120.5f)
        lineTo(204f, 120.5f)
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        strokeLineCap = StrokeCap.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(170f, 135.5f)
        lineTo(195.85f, 135.5f)
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        strokeLineCap = StrokeCap.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(170f, 151.5f)
        lineTo(195.85f, 151.5f)
      }
      path(
        stroke = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 5f,
        strokeLineCap = StrokeCap.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(122f, 29.5f)
        lineTo(134f, 29.5f)
        moveTo(128.5f, 23f)
        lineTo(128.5f, 35f)
      }
      path(
        fill = SolidColor(Color(0xFF6F6F6F)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(228f, 65.5f)
        lineToRelative(-4.11f, 2.16f)
        lineToRelative(0.79f, -4.58f)
        lineToRelative(-3.33f, -3.24f)
        lineToRelative(4.6f, -0.67f)
        lineToRelative(2.06f, -4.17f)
        lineToRelative(2.06f, 4.17f)
        lineToRelative(4.6f, 0.67f)
        lineToRelative(-3.33f, 3.24f)
        lineToRelative(0.79f, 4.58f)
        close()
      }
    }.build()

    return _NoRecordPlaceholder!!
  }

@Suppress("ObjectPropertyName")
private var _NoRecordPlaceholder: ImageVector? = null

@Preview
@Composable
private fun NoRecordPlaceholderPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.NoRecordPlaceholder, contentDescription = null)
  }
}
