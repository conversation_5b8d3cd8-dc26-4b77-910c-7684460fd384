package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.ui.icon.ValkyrieIcons
import com.example.shorts.ui.icon.WithdrawToWallet
import com.example.shorts.ui.theme.AppTheme

/**
 * 钱包页面顶部导航栏
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WalletTopBar(
  title: String = LocalStrings.current.myWallet,
  onBackClick: () -> Unit = {},
  onSetWalletAddress: () -> Unit = {},
  modifier: Modifier = Modifier
) {
  TopAppBar(
    title = {
      Text(
        text = title,
        fontSize = 18.sp,
        fontWeight = FontWeight.Medium,
        color = Color.White
      )
    },
    navigationIcon = {
      IconButton(
        onClick = onBackClick,
      ) {
        Icon(
          imageVector = Icons.AutoMirrored.Filled.ArrowBack,
          contentDescription = "Back",
        )
      }
    },
    actions = {
      // 右侧图标占位符 - 使用颜色块代替实际图标
      IconButton(
        onClick = onSetWalletAddress
      ) {
        Image(
          imageVector = ValkyrieIcons.WithdrawToWallet,
          contentDescription = "Set Wallet Address",
          modifier = Modifier.size(28.dp).offset(y = (-2).dp)
        )
      }
    },
    colors = TopAppBarDefaults.topAppBarColors(
      containerColor = Color.Transparent,
      titleContentColor = Color.White,
      navigationIconContentColor = Color.White,
      actionIconContentColor = Color.White
    ),
    modifier = modifier
  )
}

@Preview
@Composable
fun WalletTopBarPreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .background(MaterialTheme.colorScheme.background)
        .padding(16.dp)
    ) {
      WalletTopBar(
        title = "My Wallet",
        onBackClick = { },
        onSetWalletAddress = { }
      )
    }
  }
}
