package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.GameController: ImageVector
  get() {
    if (_GameController != null) {
      return _GameController!!
    }
    _GameController = ImageVector.Builder(
      name = "GameController",
      defaultWidth = 24.dp,
      defaultHeight = 24.dp,
      viewportWidth = 142f,
      viewportHeight = 134f
    ).apply {
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFD469FF),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(71f, 50.33f),
          end = Offset(57.9f, 82.63f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(108.84f, 75.13f)
        curveTo(106.35f, 53.5f, 101.95f, 38f, 87.98f, 38f)
        curveTo(80.9f, 38f, 77.26f, 45.27f, 70.94f, 45.85f)
        curveTo(64.43f, 45.27f, 60.99f, 38f, 53.91f, 38f)
        curveTo(39.93f, 38f, 35.53f, 53.5f, 33.23f, 74.94f)
        curveTo(31.89f, 86.43f, 33.04f, 96f, 40.13f, 96f)
        curveTo(50.08f, 96f, 50.46f, 87f, 57.54f, 82.79f)
        curveTo(58.89f, 82.12f, 62.59f, 78.95f, 71f, 78.95f)
        curveTo(79.41f, 78.95f, 83.39f, 82.12f, 84.72f, 82.79f)
        curveTo(91.81f, 87f, 92.19f, 96f, 102.14f, 96f)
        curveTo(109.03f, 96.19f, 109.99f, 86.62f, 108.84f, 75.13f)
        close()
      }
      path(
        fill = SolidColor(Color(0xFF4E505A)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(55.37f, 71.66f)
        curveTo(50.24f, 71.66f, 46.18f, 67.42f, 46.18f, 62.47f)
        curveTo(46.18f, 57.53f, 50.24f, 53.29f, 55.37f, 53.29f)
        curveTo(60.49f, 53.29f, 64.55f, 57.35f, 64.55f, 62.47f)
        curveTo(64.55f, 67.59f, 60.31f, 71.66f, 55.37f, 71.66f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFF4E505A)),
        strokeLineWidth = 4f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(78.78f, 62.47f)
        lineTo(97.14f, 62.47f)
      }
      path(
        stroke = SolidColor(Color(0xFF4E505A)),
        strokeLineWidth = 4f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(87.96f, 53.29f)
        lineTo(87.96f, 71.66f)
      }
    }.build()

    return _GameController!!
  }

@Suppress("ObjectPropertyName")
private var _GameController: ImageVector? = null

@Preview
@Composable
private fun GameControllerPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.GameController, contentDescription = null)
  }
}
