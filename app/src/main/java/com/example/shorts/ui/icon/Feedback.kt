package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.Feedback: ImageVector
  get() {
    if (_Feedback != null) {
      return _Feedback!!
    }
    _Feedback = ImageVector.Builder(
      name = "Feedback",
      defaultWidth = 48.dp,
      defaultHeight = 48.dp,
      viewportWidth = 48f,
      viewportHeight = 48f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(37.513f, 16.357f)
        lineTo(37.513f, 36.5f)
        curveTo(37.513f, 38.709f, 35.722f, 40.5f, 33.513f, 40.5f)
        lineTo(13f, 40.5f)
        curveTo(10.791f, 40.5f, 9f, 38.709f, 9f, 36.5f)
        lineTo(9f, 11.5f)
        curveTo(9f, 9.291f, 10.791f, 7.5f, 13f, 7.5f)
        curveTo(19.27f, 7.5f, 24.381f, 7.5f, 28.333f, 7.5f)
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(27.455f, 19.56f)
        lineTo(38.285f, 6.206f)
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(16.296f, 26.474f)
        lineTo(31.379f, 26.474f)
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(16.296f, 16.357f)
        lineTo(21.685f, 16.357f)
      }
    }.build()

    return _Feedback!!
  }

@Suppress("ObjectPropertyName")
private var _Feedback: ImageVector? = null

@Preview
@Composable
private fun FeedbackPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.Feedback, contentDescription = null)
  }
}
