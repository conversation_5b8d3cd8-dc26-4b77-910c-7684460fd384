package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.VideoIcon: ImageVector
  get() {
    if (_VideoIcon != null) {
      return _VideoIcon!!
    }
    _VideoIcon = ImageVector.Builder(
      name = "VideoIcon",
      defaultWidth = 78.dp,
      defaultHeight = 78.dp,
      viewportWidth = 78f,
      viewportHeight = 78f
    ).apply {
      path(
        fill = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(10f, 0f)
        lineTo(68f, 0f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 78f, 10f)
        lineTo(78f, 68f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 68f, 78f)
        lineTo(10f, 78f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 0f, 68f)
        lineTo(0f, 10f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 10f, 0f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color.White,
            1f to Color(0xFFE1E2E4)
          ),
          start = Offset(60.12f, 39f),
          end = Offset(19.36f, 39f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(22.25f, 18.5f)
        lineTo(55.75f, 18.5f)
        arcTo(7f, 7f, 0f, isMoreThanHalf = false, isPositiveArc = true, 62.75f, 25.5f)
        lineTo(62.75f, 52.5f)
        arcTo(7f, 7f, 0f, isMoreThanHalf = false, isPositiveArc = true, 55.75f, 59.5f)
        lineTo(22.25f, 59.5f)
        arcTo(7f, 7f, 0f, isMoreThanHalf = false, isPositiveArc = true, 15.25f, 52.5f)
        lineTo(15.25f, 25.5f)
        arcTo(7f, 7f, 0f, isMoreThanHalf = false, isPositiveArc = true, 22.25f, 18.5f)
        close()
      }
      path(
        fill = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(46.964f, 40.759f)
        lineTo(35.153f, 47.156f)
        curveTo(34.182f, 47.682f, 32.968f, 47.321f, 32.442f, 46.35f)
        curveTo(32.284f, 46.057f, 32.201f, 45.73f, 32.201f, 45.397f)
        lineTo(32.201f, 32.603f)
        curveTo(32.201f, 31.498f, 33.096f, 30.603f, 34.201f, 30.603f)
        curveTo(34.533f, 30.603f, 34.861f, 30.686f, 35.153f, 30.844f)
        lineTo(46.964f, 37.241f)
        curveTo(47.935f, 37.768f, 48.296f, 38.981f, 47.77f, 39.953f)
        curveTo(47.585f, 40.294f, 47.305f, 40.574f, 46.964f, 40.759f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(21.007f, 23.493f)
        lineTo(28.54f, 23.493f)
      }
      path(
        stroke = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(35.46f, 23.493f)
        lineTo(42.993f, 23.493f)
      }
      path(
        stroke = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(35.46f, 54.507f)
        lineTo(42.993f, 54.507f)
      }
      path(
        stroke = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(21.007f, 54.507f)
        lineTo(28.54f, 54.507f)
      }
      path(
        stroke = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(49.46f, 23.493f)
        lineTo(56.993f, 23.493f)
      }
      path(
        stroke = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 2f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(49.46f, 54.507f)
        lineTo(56.993f, 54.507f)
      }
    }.build()

    return _VideoIcon!!
  }

@Suppress("ObjectPropertyName")
private var _VideoIcon: ImageVector? = null

@Preview
@Composable
private fun VideoIconPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.VideoIcon, contentDescription = null)
  }
}
