package com.example.shorts.ui.common.dialog.loading

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.example.shorts.ui.theme.AppTheme

@Composable
fun LoadingDialog(
  onDismiss: () -> Unit
) {
  Dialog(
    onDismissRequest = onDismiss,
  ) {
    LoadingDialogContent()
  }
}


@Composable
fun LoadingDialogContent() {
  Surface(
    shape = RoundedCornerShape(8.dp),
    modifier = Modifier.size(80.dp)
  ) {
    Box(
      contentAlignment = Alignment.Center,
      modifier = Modifier.padding(16.dp)
    ) {
      CircularProgressIndicator()
    }
  }
}


@Preview
@Composable
fun LoadingDialogContentPreview() {
  AppTheme {
    LoadingDialogContent()
  }
}