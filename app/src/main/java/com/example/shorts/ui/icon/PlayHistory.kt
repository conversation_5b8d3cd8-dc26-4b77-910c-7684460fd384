package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.PlayHistory: ImageVector
  get() {
    if (_PlayHistory != null) {
      return _PlayHistory!!
    }
    _PlayHistory = ImageVector.Builder(
      name = "PlayHistory",
      defaultWidth = 48.dp,
      defaultHeight = 48.dp,
      viewportWidth = 48f,
      viewportHeight = 48f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(38.173f, 15.262f)
        curveTo(35.207f, 10.88f, 30.19f, 8f, 24.5f, 8f)
        curveTo(15.387f, 8f, 8f, 15.387f, 8f, 24.5f)
        curveTo(8f, 33.613f, 15.387f, 41f, 24.5f, 41f)
        curveTo(30.253f, 41f, 35.319f, 38.055f, 38.272f, 33.591f)
      }
      path(
        fill = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(28.699f, 26.237f)
        lineTo(22.572f, 29.732f)
        curveTo(21.613f, 30.28f, 20.391f, 29.945f, 19.844f, 28.986f)
        curveTo(19.672f, 28.684f, 19.581f, 28.342f, 19.581f, 27.995f)
        lineTo(19.581f, 21.005f)
        curveTo(19.581f, 19.9f, 20.477f, 19.005f, 21.581f, 19.005f)
        curveTo(21.929f, 19.005f, 22.27f, 19.096f, 22.572f, 19.268f)
        lineTo(28.699f, 22.763f)
        curveTo(29.659f, 23.31f, 29.993f, 24.531f, 29.446f, 25.491f)
        curveTo(29.268f, 25.802f, 29.011f, 26.06f, 28.699f, 26.237f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(34.15f, 16.153f)
        lineToRelative(4.472f, -0.627f)
        lineToRelative(0f, -5.181f)
      }
    }.build()

    return _PlayHistory!!
  }

@Suppress("ObjectPropertyName")
private var _PlayHistory: ImageVector? = null

@Preview
@Composable
private fun PlayHistoryPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.PlayHistory, contentDescription = null)
  }
}
