package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.PrivacyPolicy: ImageVector
  get() {
    if (_PrivacyPolicy != null) {
      return _PrivacyPolicy!!
    }
    _PrivacyPolicy = ImageVector.Builder(
      name = "PrivacyPolicy",
      defaultWidth = 48.dp,
      defaultHeight = 48.dp,
      viewportWidth = 48f,
      viewportHeight = 48f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(14.569f, 20.981f)
        lineTo(33.431f, 20.981f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 37.431f, 24.981f)
        lineTo(37.431f, 36f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 33.431f, 40f)
        lineTo(14.569f, 40f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 10.569f, 36f)
        lineTo(10.569f, 24.981f)
        arcTo(4f, 4f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.569f, 20.981f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(31.874f, 20.981f)
        lineTo(31.874f, 15.241f)
        curveTo(31.874f, 11.301f, 28.89f, 8f, 23.882f, 8f)
        curveTo(18.875f, 8f, 16.074f, 11.301f, 16.074f, 15.241f)
        lineTo(16.074f, 20.981f)
      }
      path(
        fill = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(26.976f, 29.258f)
        curveTo(26.743f, 30.003f, 26.222f, 30.633f, 25.52f, 31.016f)
        lineTo(25.52f, 34.088f)
        curveTo(25.52f, 34.917f, 24.825f, 35.589f, 23.966f, 35.589f)
        curveTo(23.109f, 35.589f, 22.413f, 34.917f, 22.413f, 34.088f)
        lineTo(22.413f, 31.016f)
        curveTo(21.081f, 30.237f, 20.544f, 28.629f, 21.154f, 27.245f)
        curveTo(21.764f, 25.86f, 23.335f, 25.119f, 24.84f, 25.506f)
        curveTo(26.495f, 25.981f, 27.446f, 27.653f, 26.976f, 29.258f)
        close()
      }
    }.build()

    return _PrivacyPolicy!!
  }

@Suppress("ObjectPropertyName")
private var _PrivacyPolicy: ImageVector? = null

@Preview
@Composable
private fun PrivacyPolicyPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.PrivacyPolicy, contentDescription = null)
  }
}
