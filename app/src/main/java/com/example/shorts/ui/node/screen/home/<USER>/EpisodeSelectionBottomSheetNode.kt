package com.example.shorts.ui.node.screen.home.shorts

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import coil3.compose.AsyncImage
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.guia.BottomSheetNode
import com.roudikk.guia.core.BottomSheet
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.Parcelize

@Parcelize
class EpisodeSelectionBottomSheetNode(
  private val shortPlay: ShortPlay,
  private val currentEpisode: Int = 1,
  private val onEpisodeSelected: (Int) -> Unit
) : BottomSheetNode(
  tag = "episode_selection_bottom_sheet"
) {
  @Composable
  override fun Content(navigator: Navigator, bottomSheet: BottomSheet?) {
    EpisodeSelectionContent(
      shortPlay = shortPlay,
      currentEpisode = currentEpisode,
      onEpisodeSelected = { episode ->
        onEpisodeSelected(episode)
        navigator.pop()
      },
      onDismiss = { navigator.pop() }
    )
  }
}

@Composable
private fun EpisodeSelectionContent(
  shortPlay: ShortPlay,
  currentEpisode: Int,
  onEpisodeSelected: (Int) -> Unit,
  onDismiss: () -> Unit
) {
  val strings = LocalStrings.current
  Surface(
    modifier = Modifier.fillMaxWidth().systemBarsPadding(),
    shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
    shadowElevation = 8.dp
  ) {
    Column(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp)
    ) {
    // 顶部标题栏
    Row(
      modifier = Modifier.fillMaxWidth(),
      horizontalArrangement = Arrangement.SpaceBetween,
      verticalAlignment = Alignment.CenterVertically
    ) {
      Text(
        text = strings.selectEpisode,
        fontSize = 18.sp,
        fontWeight = FontWeight.Bold,
        color = MaterialTheme.colorScheme.onSurface
      )
      
      IconButton(onClick = onDismiss) {
        Icon(
          imageVector = Icons.Default.Close,
          contentDescription = strings.close,
          tint = MaterialTheme.colorScheme.onSurface
        )
      }
    }
    
    Spacer(modifier = Modifier.height(16.dp))
    
    // 剧集信息
    Row(
      modifier = Modifier.fillMaxWidth(),
      verticalAlignment = Alignment.CenterVertically
    ) {
      // 封面图片
      AsyncImage(
        model = shortPlay.coverImage,
        contentDescription = shortPlay.title,
        modifier = Modifier
          .size(80.dp, 100.dp)
          .clip(RoundedCornerShape(8.dp)),
        contentScale = ContentScale.Crop
      )
      
      Spacer(modifier = Modifier.width(12.dp))
      
      // 剧集信息
      Column(
        modifier = Modifier.weight(1f)
      ) {
        Text(
          text = shortPlay.title,
          fontSize = 16.sp,
          fontWeight = FontWeight.Bold,
          color = MaterialTheme.colorScheme.onSurface,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis
        )
        
        Spacer(modifier = Modifier.height(4.dp))
        
        Text(
          text = strings.totalEpisodes(shortPlay.total),
          fontSize = 14.sp,
          color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
        )
      }
    }
    
    Spacer(modifier = Modifier.height(16.dp))
    
    // 剧集网格
    LazyVerticalGrid(
      columns = GridCells.Fixed(5),
      horizontalArrangement = Arrangement.spacedBy(8.dp),
      verticalArrangement = Arrangement.spacedBy(8.dp),
      modifier = Modifier.height(320.dp) // 限制高度
    ) {
      items((1..shortPlay.total).toList()) { episode ->
        EpisodeItemContent(
          episode = episode,
          isCurrentEpisode = episode == currentEpisode,
          onClick = { onEpisodeSelected(episode) }
        )
      }
    }
    
    } // Column 闭合
  } // Surface 闭合
}

@Composable
private fun EpisodeItemContent(
  episode: Int,
  isCurrentEpisode: Boolean,
  onClick: () -> Unit
) {
  val strings = LocalStrings.current
  Box(
    modifier = Modifier
      .aspectRatio(3/2f)
      .clip(RoundedCornerShape(8.dp))
      .background(
        if (isCurrentEpisode) {
          MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)
        } else {
          MaterialTheme.colorScheme.surfaceVariant
        }
      )
      .border(
        border = if (isCurrentEpisode) {
          BorderStroke(1.dp, MaterialTheme.colorScheme.primary)
        } else {
          BorderStroke(0.dp, Color.Transparent)
        },
        shape = RoundedCornerShape(8.dp)
      )
      .clickable { onClick() },
    contentAlignment = Alignment.Center
  ) {
    Text(
      text = episode.toString(),
      fontSize = 16.sp,
      fontWeight = if (isCurrentEpisode) FontWeight.Bold else FontWeight.Normal,
      color = if (isCurrentEpisode) {
        MaterialTheme.colorScheme.primary
      } else {
        MaterialTheme.colorScheme.onSurface
      }
    )
    
    // 当前播放指示器
    if (isCurrentEpisode) {
      Icon(
        imageVector = Icons.Default.PlayArrow,
        contentDescription = strings.nowPlaying,
        modifier = Modifier
          .size(14.dp)
          .align(Alignment.TopEnd)
          .offset((-4).dp, 4.dp),
        tint = MaterialTheme.colorScheme.primary
      )
    }
  }
}
