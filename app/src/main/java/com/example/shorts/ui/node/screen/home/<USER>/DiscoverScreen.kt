package com.example.shorts.ui.node.screen.home.discover

import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import coil3.compose.AsyncImage
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.mvi_ui_model.koinUiModel
import com.example.shorts.ui.common.withdraw.WithdrawBar
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.example.shorts.ui.node.screen.wallet.WalletNode
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.requireLocalNavigator
import org.orbitmvi.orbit.compose.collectAsState

@Composable
fun DiscoverScreen(
  modifier: Modifier = Modifier,
  uiModel: DiscoverUiModel = koinUiModel()
) {
  val navigator = requireLocalNavigator()
  val uiState by uiModel.collectAsState()

  DiscoverContent(
    uiState = uiState,
    onCategorySelected = uiModel::onCategorySelected,
    onLoadMoreCategoryDramas = uiModel::onLoadMoreCategoryDramas,
    onDramaClick = { drama ->
      navigator.push(DramaPlayScreenNode(drama))
    },
    onWithdrawClick = {
      navigator.push(WalletNode())
    },
    modifier = modifier
  )
}

@Composable
private fun DiscoverContent(
  uiState: DiscoverUiState,
  onCategorySelected: (Long) -> Unit,
  onLoadMoreCategoryDramas: () -> Unit,
  onDramaClick: (ShortPlay) -> Unit,
  onWithdrawClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val screenHeight = LocalWindowInfo.current.containerSize.height

  when {
    uiState.isLoading && uiState.recommendedDramas.isEmpty() -> {
      Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        CircularProgressIndicator()
      }
    }

    uiState.error != null && uiState.recommendedDramas.isEmpty() -> {
      Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
      ) {
        Text(
          text = uiState.error,
          style = MaterialTheme.typography.bodyLarge,
          color = MaterialTheme.colorScheme.error
        )
      }
    }

    else -> {
      // Use LazyColumn instead of Column with verticalScroll to fix the scrolling issue
      LazyColumn(modifier = modifier) {
        // Withdraw bar at the top
        item {
          WithdrawBar(
            onWithdrawClick = onWithdrawClick,
            modifier = Modifier
              .padding(top = 16.dp)
              .padding(horizontal = 16.dp)
          )
        }

        // Recommended for you section
        item {
          RecommendedSection(
            dramas = uiState.recommendedDramas,
            onDramaClick = onDramaClick,
            modifier = Modifier.padding(top = 16.dp)
          )
        }

        // New Releases section
        item {
          NewReleasesSection(
            dramas = uiState.newReleases,
            onDramaClick = onDramaClick,
            modifier = Modifier.padding(top = 16.dp)
          )
        }

        // Categories section
        item {
          CategoriesSection(
            categories = uiState.categories,
            selectedCategoryId = uiState.selectedCategoryId,
            onCategorySelected = onCategorySelected,
            isLoading = uiState.isLoadingCategories,
            modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
          )
        }

        // Category dramas grid - using items instead of item to enable proper scrolling
        if (uiState.categoryDramas.isNotEmpty()) {
          items(
            items = uiState.categoryDramas.chunked(2), // Group items in pairs for 2-column layout
            key = { dramasPair -> dramasPair.firstOrNull()?.id ?: 0 }
          ) { dramasPair ->
            Row(
              modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 6.dp),
              horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
              dramasPair.forEach { drama ->
                DramaGridItem(
                  drama = drama,
                  onClick = { onDramaClick(drama) },
                  modifier = Modifier.weight(1f)
                )
              }
              // Fill empty space if odd number of items
              if (dramasPair.size == 1) {
                Spacer(modifier = Modifier.weight(1f))
              }
            }
          }
        }

        // Loading indicator for category dramas
        if (uiState.isLoadingCategoryDramas) {
          item {
            Box(
              modifier = Modifier
                .fillMaxWidth()
                .height(if (uiState.categoryDramas.isEmpty().not()) 64.dp else screenHeight.dp)
                .padding(16.dp),
            ) {
              CircularProgressIndicator(
                modifier = Modifier
                  .align(Alignment.TopCenter)
                  .padding(top = 8.dp)
              )
            }
          }
        }

        // Load more trigger - invisible item that triggers loading when reached
        if (uiState.hasMoreCategoryDramas && !uiState.isLoadingCategoryDramas) {
          item {
            LaunchedEffect(uiState.selectedCategoryId) {
              onLoadMoreCategoryDramas()
            }
            Spacer(modifier = Modifier.height(1.dp))
          }
        }

        // Bottom spacing
        item {
          Spacer(modifier = Modifier.height(16.dp))
        }
      }
    }
  }
}

// UI Components

// Composable sections
@Composable
private fun RecommendedSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(modifier) {
    Text(
      text = strings.recommendedForYou,
      fontSize = 17.sp,
      fontWeight = FontWeight.Bold,
      modifier = Modifier
        .padding(bottom = 12.dp)
        .padding(horizontal = 16.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      item { Spacer(modifier = Modifier.width(4.dp)) }

      items(dramas) { drama ->
        DramaCard(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }

      item { Spacer(modifier = Modifier.width(4.dp)) }
    }
  }
}

@Composable
private fun NewReleasesSection(
  dramas: List<ShortPlay>,
  onDramaClick: (ShortPlay) -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(modifier) {
    Text(
      text = strings.newReleases,
      fontSize = 17.sp,
      fontWeight = FontWeight.Bold,
      modifier = Modifier
        .padding(bottom = 12.dp)
        .padding(horizontal = 16.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
      item { Spacer(modifier = Modifier.width(4.dp)) }

      items(dramas) { drama ->
        DramaCard(
          drama = drama,
          onClick = { onDramaClick(drama) }
        )
      }

      item { Spacer(modifier = Modifier.width(4.dp)) }
    }
  }
}

@Composable
private fun CategoriesSection(
  categories: List<ShortPlay.ShortPlayCategory>,
  selectedCategoryId: Long?,
  onCategorySelected: (Long) -> Unit,
  isLoading: Boolean = false,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(modifier) {
    Text(
      text = strings.categories,
      fontSize = 17.sp,
      fontWeight = FontWeight.Bold,
      modifier = Modifier
        .padding(bottom = 4.dp)
        .padding(horizontal = 16.dp)
    )

    LazyRow(
      horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      item { Spacer(modifier = Modifier.width(8.dp)) }

      items(categories) { category ->
        CategoryChip(
          category = category,
          isSelected = category.id == selectedCategoryId,
          onClick = { onCategorySelected(category.id) }
        )
      }

      item { Spacer(modifier = Modifier.width(8.dp)) }
    }
  }
}


@Composable
private fun DramaCard(
  drama: ShortPlay,
  onClick: () -> Unit
) {
  val strings = LocalStrings.current

  Card(
    modifier = Modifier
      .width(120.dp)
      .clickable(onClick = onClick),
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .aspectRatio(3 / 4f),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier.padding(horizontal = 8.dp, vertical = 6.dp)
      ) {
        Text(
          text = drama.title + "\n",
          fontSize = 12.sp,
          lineHeight = 16.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
        )

        Text(
          text = strings.episodesCount(drama.total),
          fontSize = 10.sp,
          lineHeight = 13.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 4.dp)
        )
      }
    }
  }
}

@Composable
private fun DramaGridItem(
  drama: ShortPlay,
  onClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Card(
    modifier = modifier
      .fillMaxWidth()
      .clickable { onClick() },
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
  ) {
    Column {
      AsyncImage(
        model = drama.coverImage,
        contentDescription = drama.title,
        modifier = Modifier
          .fillMaxWidth()
          .aspectRatio(3 / 4f),
        contentScale = ContentScale.Crop
      )

      Column(
        modifier = Modifier
          .padding(vertical = 8.dp, horizontal = 8.dp)
      ) {
        Text(
          text = drama.title + "\n",
          fontSize = 13.sp,
          lineHeight = 17.sp,
          fontWeight = FontWeight.Medium,
          maxLines = 2,
          overflow = TextOverflow.Ellipsis,
        )

        Text(
          text = strings.episodesCount(drama.total),
          fontSize = 10.sp,
          lineHeight = 14.sp,
          color = Color.Gray,
          modifier = Modifier.padding(top = 3.dp)
        )
      }
    }
  }
}

@Composable
private fun CategoryChip(
  category: ShortPlay.ShortPlayCategory,
  isSelected: Boolean,
  onClick: () -> Unit
) {
  val (backgroundColor, textColor) = if (isSelected) {
    MaterialTheme.colorScheme.primary to MaterialTheme.colorScheme.onPrimary
  } else {
    MaterialTheme.colorScheme.surface to MaterialTheme.colorScheme.onSurface
  }

  Surface(
    onClick = onClick,
    color = backgroundColor,
    shape = RoundedCornerShape(8.dp)
  ) {
    Text(
      text = category.name,
      modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
      fontSize = 12.sp,
      lineHeight = 16.sp,
      color = textColor,
      fontWeight = if (isSelected) FontWeight.SemiBold else FontWeight.Normal
    )
  }
}