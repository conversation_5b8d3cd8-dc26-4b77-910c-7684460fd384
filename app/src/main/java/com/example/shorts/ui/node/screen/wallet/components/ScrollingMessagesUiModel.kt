package com.example.shorts.ui.node.screen.wallet.components

import com.example.shorts.foundation.earn.WithdrawalOptions
import com.example.shorts.foundation.mvi_ui_model.UiModel
import com.example.shorts.res.AppRes
import com.example.shorts.res.namePool
import com.example.shorts.ui.node.screen.wallet.ScrollingMessage
import org.koin.android.annotation.KoinViewModel
import java.util.UUID

@KoinViewModel
class ScrollingMessagesUiModel :
  UiModel<ScrollingMessagesUiState, Nothing>(ScrollingMessagesUiState()) {

  init {
    createMessage()
  }

  fun createMessage() = intent {
    val messages = (1..200).map {
      val randomName = AppRes.namePool.random()
      val randomWithdrawal = WithdrawalOptions.random()

      ScrollingMessage(
        id = UUID.randomUUID().toString(),
        userName = randomName,
        withdrawal = randomWithdrawal
      )
    }

    reduce { state.copy(messages = messages) }
  }
}