package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.VideoOutlined: ImageVector
  get() {
    if (_VideoOutlined != null) {
      return _VideoOutlined!!
    }
    _VideoOutlined = ImageVector.Builder(
      name = "VideoOutlined",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(15.306f, 13.5f)
        lineTo(54.694f, 13.5f)
        arcTo(5f, 5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 59.694f, 18.5f)
        lineTo(59.694f, 51.5f)
        arcTo(5f, 5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 54.694f, 56.5f)
        lineTo(15.306f, 56.5f)
        arcTo(5f, 5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 10.306f, 51.5f)
        lineTo(10.306f, 18.5f)
        arcTo(5f, 5f, 0f, isMoreThanHalf = false, isPositiveArc = true, 15.306f, 13.5f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(41.999f, 38.055f)
        curveTo(42.134f, 38.631f, 42.014f, 39.268f, 41.597f, 39.78f)
        lineTo(31.534f, 46.157f)
        curveTo(31.065f, 46.449f, 30.522f, 46.522f, 30.024f, 46.406f)
        curveTo(29.525f, 46.29f, 29.071f, 45.983f, 28.78f, 45.514f)
        curveTo(28.583f, 45.197f, 28.478f, 44.831f, 28.478f, 44.458f)
        lineTo(28.478f, 32.561f)
        curveTo(28.478f, 32.008f, 28.702f, 31.508f, 29.064f, 31.146f)
        curveTo(29.426f, 30.784f, 29.926f, 30.561f, 30.478f, 30.561f)
        curveTo(30.851f, 30.561f, 31.217f, 30.665f, 31.534f, 30.862f)
        lineTo(41.107f, 36.811f)
        curveTo(41.576f, 37.102f, 41.883f, 37.556f, 41.999f, 38.055f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(11.37f, 23.054f)
        lineTo(58.63f, 23.054f)
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(28.351f, 13.553f)
        lineTo(23.201f, 22.523f)
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(45.722f, 13.553f)
        lineTo(40.573f, 22.523f)
      }
    }.build()

    return _VideoOutlined!!
  }

@Suppress("ObjectPropertyName")
private var _VideoOutlined: ImageVector? = null

@Preview
@Composable
private fun VideoOutlinedPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.VideoOutlined, contentDescription = null)
  }
}
