package com.example.shorts.ui.node.screen.home.shorts.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.bytedance.sdk.shortplay.api.ShortPlayFragment
import com.example.shorts.foundation.kermit.debugLog

@Composable
fun VideoPlayerScreen(
  shortPlay: ShortPlay,
  onBackClick: () -> Unit,
  modifier: Modifier = Modifier
) {
  val context = LocalContext.current
  val activity = context as? FragmentActivity
  
  var detailFragment by remember { mutableStateOf<ShortPlayFragment?>(null) }
  var isLoading by remember { mutableStateOf(true) }
  var error by remember { mutableStateOf<String?>(null) }

  LaunchedEffect(shortPlay) {
    if (activity != null) {
      try {
        val builder = PSSDK.DetailPageConfig.Builder()
        builder
          .hideLeftTopCloseAndTitle(true, null)
          .displayBottomExtraView(false)
          .displayProgressBar(true)
          .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_TITLE, true)
          .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_DESC, true)
          .playSingleItem(false)
          .enableImmersiveMode(5000)

        val fragment = PSSDK.createDetailFragment(shortPlay, builder.build(), object : PSSDK.ShortPlayDetailPageListener {
          override fun onShortPlayPlayed(shortPlay: ShortPlay, index: Int) {
            debugLog(tag = "VideoPlayerScreen") { "Playing episode $index of ${shortPlay.title}" }
          }

          override fun onOverScroll(direction: Int) {
            debugLog(tag = "VideoPlayerScreen") { "Over scroll direction: $direction" }
          }

          override fun onProgressChange(shortPlay: ShortPlay, index: Int, currentPlayTime: Int, duration: Int) {
            // 播放进度变化
          }

          override fun onPlayFailed(errorInfo: PSSDK.ErrorInfo): Boolean {
            debugLog(tag = "VideoPlayerScreen") { "Play failed: ${errorInfo.code} - ${errorInfo.msg}" }
            error = "播放失败: ${errorInfo.msg}"
            return false
          }

          override fun onItemSelected(position: Int, type: PSSDK.ShortPlayDetailPageListener.ItemType, index: Int) {
            debugLog(tag = "VideoPlayerScreen") { "Item selected: position=$position, type=$type, index=$index" }
          }

          override fun onVideoPlayStateChanged(shortPlay: ShortPlay, index: Int, playbackState: Int) {
            debugLog(tag = "VideoPlayerScreen") { "Play state changed: $playbackState" }
          }

          override fun onVideoPlayCompleted(shortPlay: ShortPlay, index: Int) {
            debugLog(tag = "VideoPlayerScreen") { "Video completed: episode $index" }
          }

          override fun onEnterImmersiveMode() {
            debugLog(tag = "VideoPlayerScreen") { "Entered immersive mode" }
          }

          override fun onExitImmersiveMode() {
            debugLog(tag = "VideoPlayerScreen") { "Exited immersive mode" }
          }

          override fun isNeedBlock(shortPlay: ShortPlay, index: Int): Boolean {
            return false
          }

          override fun showAdIfNeed(shortPlay: ShortPlay, index: Int, listener: PSSDK.ShortPlayBlockResultListener) {
            listener.onShortPlayUnlocked()
          }

          override fun onVideoInfoFetched(shortPlay: ShortPlay, index: Int, videoPlayInfo: PSSDK.VideoPlayInfo) {
            debugLog(tag = "VideoPlayerScreen") { "Video info fetched for episode $index" }
          }

          override fun onObtainPlayerControlViews(): List<android.view.View>? {
            return null
          }
        })

        detailFragment = fragment
        isLoading = false
      } catch (e: Exception) {
        debugLog(tag = "VideoPlayerScreen") { "Failed to create detail fragment: ${e.message}" }
        error = "创建播放器失败: ${e.message}"
        isLoading = false
      }
    } else {
      error = "无法获取Activity实例"
      isLoading = false
    }
  }

  Box(
    modifier = modifier
      .fillMaxSize()
      .background(Color.Black)
  ) {
    when {
      isLoading -> {
        CircularProgressIndicator(
          modifier = Modifier.align(Alignment.Center),
          color = Color.White
        )
      }
      
      error != null -> {
        Column(
          modifier = Modifier.align(Alignment.Center),
          horizontalAlignment = Alignment.CenterHorizontally
        ) {
          Text(
            text = error!!,
            color = Color.White,
            style = MaterialTheme.typography.bodyLarge
          )
          Spacer(modifier = Modifier.height(16.dp))
          Button(onClick = onBackClick) {
            Text("返回")
          }
        }
      }
      
      detailFragment != null && activity != null -> {
        AndroidView(
          factory = { context ->
            FragmentContainerView(context).apply {
              id = android.view.View.generateViewId()
            }
          },
          modifier = Modifier.fillMaxSize()
        ) { fragmentContainer ->
          val fragmentManager = activity.supportFragmentManager
          val transaction = fragmentManager.beginTransaction()
          transaction.replace(fragmentContainer.id, detailFragment!!)
          transaction.commitNow()
        }
      }
    }

    // 自定义返回按钮
    IconButton(
      onClick = onBackClick,
      modifier = Modifier
        .align(Alignment.TopStart)
        .padding(16.dp)
        .background(
          Color.Black.copy(alpha = 0.5f),
          androidx.compose.foundation.shape.CircleShape
        )
    ) {
      Icon(
        imageVector = Icons.Default.ArrowBack,
        contentDescription = "返回",
        tint = Color.White
      )
    }
  }

  DisposableEffect(detailFragment) {
    onDispose {
      if (activity != null && detailFragment != null) {
        try {
          val fragmentManager = activity.supportFragmentManager
          val transaction = fragmentManager.beginTransaction()
          transaction.remove(detailFragment!!)
          transaction.commitNow()
        } catch (e: Exception) {
          debugLog(tag = "VideoPlayerScreen") { "Failed to remove fragment: ${e.message}" }
        }
      }
    }
  }
}
