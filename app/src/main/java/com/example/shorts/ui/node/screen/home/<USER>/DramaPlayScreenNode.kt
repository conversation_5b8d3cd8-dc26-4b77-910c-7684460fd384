package com.example.shorts.ui.node.screen.home.shorts

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import android.view.View
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.compose.LifecycleEventEffect
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.bytedance.sdk.shortplay.api.ShortPlayFragment
import com.example.shorts.R
import com.example.shorts.foundation.guia.ScreenNode
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.android.findActivity
import com.example.shorts.ui.node.screen.home.shorts.components.ShortsAdapterManager
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.push
import com.roudikk.guia.extensions.pop
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

@Parcelize
class DramaPlayScreenNode(
  private val shortPlay: ShortPlay,
  private val startEpisode: Int = 1,
  private val startFromSeconds: Int = 0
) : ScreenNode("drama_play_screen") {


  @IgnoredOnParcel
  private var cachedSystemBarsBehavior: Int? = null

  @Composable
  override fun Content(navigator: Navigator) {
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    val activity = remember(context) { context.findActivity() as? FragmentActivity }
    val view = LocalView.current

    var currentEpisode by remember { mutableIntStateOf(startEpisode) }
    var detailFragment by remember { mutableStateOf<ShortPlayFragment?>(null) }

    var immersiveMode: Boolean by remember { mutableStateOf(false) }
    val currentPlayTimeState = remember { mutableIntStateOf(0) }
    var duration by remember { mutableIntStateOf(0) }

    // Hide system bars when entering this screen
    SideEffect {
      activity?.window?.let { window ->
        val windowInsetsController = WindowCompat.getInsetsController(window, view)
        windowInsetsController.apply {
          cachedSystemBarsBehavior = systemBarsBehavior
          hide(WindowInsetsCompat.Type.statusBars() or WindowInsetsCompat.Type.navigationBars())
          systemBarsBehavior = WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        }
      }
    }

    // Restore system bars when leaving this screen
    DisposableEffect(Unit) {
      onDispose {
        activity?.window?.let { window ->
          val windowInsetsController = WindowCompat.getInsetsController(window, view)
          windowInsetsController.apply {
            show(WindowInsetsCompat.Type.statusBars() or WindowInsetsCompat.Type.navigationBars())
            cachedSystemBarsBehavior?.let { systemBarsBehavior = it }
          }
        }
      }
    }

    LifecycleEventEffect(event = Lifecycle.Event.ON_START) {
      activity?.let {
        ShortsAdapterManager.getDefaultAdapter()?.getCurrentFragment()?.apply {
          setMuted(true)
        }
      }
    }

//    LifecycleEventEffect(Lifecycle.Event.ON_RESUME) {
//      scope.launch {
//        repeat(5) {
//          delay(100)
//          activity?.let {
//            ShortsAdapterManager.getDefaultAdapter()?.getCurrentFragment()?.apply {
//              pausePlay()
//              setMuted(true)
//            }
//          }
//        }
//      }
//    }

    LaunchedEffect(shortPlay, currentEpisode) {
      currentPlayTimeState.intValue = 0
      duration = 0

      if (activity != null) {
        debugLog(tag = "DramaPlayScreenNode") {
          "Creating fragment for ${shortPlay.title}, episode $currentEpisode"
        }

        // 创建播放Fragment
        val config = PSSDK.DetailPageConfig.Builder().apply {
          hideLeftTopCloseAndTitle(false) {
            navigator.pop()
            true
          }
          displayBottomExtraView(false)
          displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_DESC, true)
          displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_TITLE, true)
          displayProgressBar(false)
          progressBarMarginToBottom(20)
          startPlayIndex(currentEpisode)
          startPlayAtTimeSeconds(startFromSeconds)
          enableImmersiveMode(3000) // 播放3秒后进入沉浸模式
          enableAutoPlayNext(true)
        }.build()

        val fragment =
          PSSDK.createDetailFragment(shortPlay, config, object : PSSDK.ShortPlayDetailPageListener {
            override fun onShortPlayPlayed(shortPlay: ShortPlay, index: Int) {
              debugLog(tag = "DramaPlayScreenNode") { "Playing episode $index of ${shortPlay.title}" }
              currentEpisode = index
            }

            override fun onOverScroll(direction: Int) {}
            override fun onProgressChange(
              shortPlay: ShortPlay,
              index: Int,
              currentPlayTimeValue: Int,
              durationValue: Int
            ) {
              currentPlayTimeState.intValue = currentPlayTimeValue
              duration = durationValue
            }

            override fun onPlayFailed(errorInfo: PSSDK.ErrorInfo): Boolean = false
            override fun onItemSelected(
              position: Int,
              type: PSSDK.ShortPlayDetailPageListener.ItemType,
              index: Int
            ) {
            }

            override fun onVideoPlayStateChanged(
              shortPlay: ShortPlay,
              index: Int,
              playbackState: Int
            ) {
              if (playbackState == PSSDK.PLAYBACK_STATE_PAUSE) {
                immersiveMode = false
              }
            }

            override fun onVideoPlayCompleted(shortPlay: ShortPlay, index: Int) {}
            override fun onEnterImmersiveMode() {
              immersiveMode = true
            }

            override fun onExitImmersiveMode() {
              immersiveMode = false
            }

            override fun isNeedBlock(shortPlay: ShortPlay, index: Int): Boolean = false
            override fun showAdIfNeed(
              shortPlay: ShortPlay,
              index: Int,
              listener: PSSDK.ShortPlayBlockResultListener
            ) {
            }

            override fun onVideoInfoFetched(
              shortPlay: ShortPlay,
              index: Int,
              videoPlayInfo: PSSDK.VideoPlayInfo
            ) {
            }

            override fun onObtainPlayerControlViews(): MutableList<View> = mutableListOf()
          })
        detailFragment = fragment
      }
    }


    DramaPlayContent(
      shortPlay = shortPlay,
      currentEpisode = currentEpisode,
      detailFragment = detailFragment,
      activity = activity,
      immersiveMode = immersiveMode,
      currentPlayTimeState = currentPlayTimeState,
      duration = duration,
      onBackClick = navigator::pop,
      onEpisodeSelectionClick = {
        navigator.push(
          EpisodeSelectionBottomSheetNode(
            shortPlay = shortPlay,
            currentEpisode = currentEpisode,
            onEpisodeSelected = { episode ->
              currentEpisode = episode
            }
          )
        )
      }
    )
  }
}

@Composable
private fun DramaPlayContent(
  shortPlay: ShortPlay,
  currentEpisode: Int,
  detailFragment: ShortPlayFragment?,
  activity: FragmentActivity?,
  immersiveMode: Boolean,
  currentPlayTimeState: MutableIntState,
  duration: Int,
  onBackClick: () -> Unit,
  onEpisodeSelectionClick: () -> Unit
) {
  Scaffold {
    Box(
      modifier = Modifier
        .fillMaxSize()
        .padding(it)
    ) {
      // 视频播放器
      when {
        detailFragment == null -> {
          // 加载中状态
          Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
          ) {
            CircularProgressIndicator()
          }
        }

        activity != null -> {
          AndroidView(
            factory = { context ->
              FragmentContainerView(context).apply {
                id = View.generateViewId()
              }
            },
            modifier = Modifier.fillMaxSize(),
            onRelease = { fragmentContainer ->
              val fragmentManager = activity.supportFragmentManager
              val transaction = fragmentManager.beginTransaction()
              transaction.remove(detailFragment).commitNow()
            }
          ) { fragmentContainer ->
            val fragmentManager = activity.supportFragmentManager
            val transaction = fragmentManager.beginTransaction()
            transaction.replace(fragmentContainer.id, detailFragment)
            transaction.commitNow()
          }
        }
      }

      if (immersiveMode.not()) {
        // Custom Progress Bar
        CustomProgressBar(
          currentPlayTime = currentPlayTimeState.intValue,
          duration = duration,
          onSeek = { seekTimeInSeconds ->
            debugLog(tag = "DramaPlayScreenNode") { "Seeking to $seekTimeInSeconds seconds" }
            currentPlayTimeState.intValue = seekTimeInSeconds
            detailFragment?.setCurrentPlayTimeSeconds(seekTimeInSeconds)
          },
          modifier = Modifier
            .align(Alignment.BottomCenter)
            .padding(horizontal = 16.dp)
            .padding(bottom = 26.dp)
            .fillMaxWidth()
        )

        IconButton(
          onClick = onEpisodeSelectionClick,
          modifier = Modifier
            .align(Alignment.BottomEnd)
            .padding(bottom = 70.dp, end = 16.dp)
            .size(20.dp)
            .background(
              Color.Black.copy(alpha = 0.5f),
              CircleShape
            )
        ) {
          Icon(
            painter = painterResource(R.drawable.ic_episodes),
            contentDescription = null,
            tint = Color.White
          )
        }
      }

//      // 底部剧集信息
//      Card(
//        modifier = Modifier
//          .align(Alignment.BottomStart)
//          .padding(bottom = 64.dp, start = 16.dp),
//        colors = CardDefaults.cardColors(
//          containerColor = Color.Black.copy(alpha = 0.7f)
//        )
//      ) {
//        Column(
//          modifier = Modifier.padding(12.dp)
//        ) {
//          Text(
//            text = shortPlay.title,
//            color = Color.White,
//            style = MaterialTheme.typography.titleMedium
//          )
//          Text(
//            text = "第 $currentEpisode 集 / 共 ${shortPlay.total} 集",
//            color = Color.White.copy(alpha = 0.8f),
//            style = MaterialTheme.typography.bodySmall
//          )
//        }
//      }
    }
  }
}

@Composable
private fun CustomProgressBar(
  currentPlayTime: Int,
  duration: Int,
  onSeek: (Int) -> Unit,
  modifier: Modifier = Modifier
) {
  var isDragging by remember { mutableStateOf(false) }
  var dragProgress by remember { mutableFloatStateOf(0f) }
  var progressBarSize by remember { mutableStateOf(IntSize.Zero) }
  val density = LocalDensity.current

  val currentProgress = if (duration > 0) {
    (currentPlayTime.toFloat() / duration.toFloat()).coerceIn(0f, 1f)
  } else {
    0f
  }

  val displayProgress = if (isDragging) dragProgress else currentProgress

  val animatedProgress by animateFloatAsState(
    targetValue = displayProgress,
    animationSpec = tween(
      durationMillis = if (isDragging) 0 else 200,
      easing = LinearEasing
    ),
    label = "progress_animation"
  )

  Box(
    modifier = modifier
      .height(12.dp) // Increased height for better touch target
      .onSizeChanged { size ->
        progressBarSize = size
      }
      .pointerInput(Unit) {
        detectDragGestures(
          onDragStart = { offset ->
            isDragging = true
            val progress = (offset.x / progressBarSize.width.toFloat()).coerceIn(0f, 1f)
            dragProgress = progress
          },
          onDrag = { change, _ ->
            val newProgress = (change.position.x / progressBarSize.width.toFloat()).coerceIn(0f, 1f)
            dragProgress = newProgress
          },
          onDragEnd = {
            if (duration > 0) {
              val seekTime = (dragProgress * duration).toInt()
              onSeek(seekTime)
            }
            isDragging = false
          }
        )
      }
      .pointerInput(Unit) {
        // Handle single tap
        detectTapGestures { offset ->
          val progress = (offset.x / progressBarSize.width.toFloat()).coerceIn(0f, 1f)
          if (duration > 0) {
            val seekTime = (progress * duration).toInt()
            onSeek(seekTime)
          }
        }
      }
  ) {
    // Background track
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .height(4.dp)
        .align(Alignment.Center)
        .background(
          Color.White.copy(alpha = 0.3f),
          RoundedCornerShape(2.dp)
        )
        .clip(RoundedCornerShape(2.dp))
    )

    // Progress track
    Box(
      modifier = Modifier
        .fillMaxWidth(animatedProgress)
        .height(4.dp)
        .align(Alignment.CenterStart)
        .background(
          Color.White,
          RoundedCornerShape(2.dp)
        )
    )

    // Thumb (always visible dragger)
    Box(
      modifier = Modifier
        .size(12.dp)
        .offset(
          x = with(density) {
            (progressBarSize.width.toDp() * animatedProgress - 6.dp).coerceAtLeast(0.dp)
          }
        )
        .align(Alignment.CenterStart)
        .background(
          Color.White,
          CircleShape
        )
    )
  }
}
