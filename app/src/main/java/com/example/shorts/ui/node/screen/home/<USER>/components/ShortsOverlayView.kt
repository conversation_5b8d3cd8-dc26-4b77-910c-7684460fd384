package com.example.shorts.ui.node.screen.home.shorts.components

import android.content.Context
import android.graphics.Color
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.bytedance.sdk.shortplay.api.ShortPlayFragment

class ShortsOverlayView(
  context: Context,
  private val shortPlay: ShortPlay
) : FrameLayout(context), PSSDK.IControlView {

  private val titleTextView: TextView
  private val descTextView: TextView
  private val episodeTextView: TextView
  private var shortPlayFragment: ShortPlayFragment? = null

  init {
    // 创建底部信息容器
    val bottomContainer = LinearLayout(context).apply {
      orientation = LinearLayout.VERTICAL
      setPadding(dpToPx(16), 0, dpToPx(80), dpToPx(100)) // 右边留空间给按钮
    }

    // 剧集信息
    episodeTextView = TextView(context).apply {
      textSize = 14f
      setTextColor(Color.WHITE)
      setShadowLayer(2f, 1f, 1f, Color.BLACK)
      text = "${shortPlay.total}集 - ${shortPlay.title}"
    }

    // 标题
    titleTextView = TextView(context).apply {
      textSize = 18f
      setTextColor(Color.WHITE)
      setShadowLayer(2f, 1f, 1f, Color.BLACK)
      text = shortPlay.title
      maxLines = 2
    }

    // 描述
    descTextView = TextView(context).apply {
      textSize = 14f
      setTextColor(Color.parseColor("#CCFFFFFF"))
      setShadowLayer(2f, 1f, 1f, Color.BLACK)
      text = shortPlay.desc
      maxLines = 3
    }

    bottomContainer.addView(episodeTextView)
    bottomContainer.addView(titleTextView, LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    ).apply {
      topMargin = dpToPx(8)
    })
    bottomContainer.addView(descTextView, LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    ).apply {
      topMargin = dpToPx(4)
    })

    // 添加到底部
    addView(bottomContainer, FrameLayout.LayoutParams(
      FrameLayout.LayoutParams.MATCH_PARENT,
      FrameLayout.LayoutParams.WRAP_CONTENT
    ).apply {
      gravity = Gravity.BOTTOM
    })

    // 点击剧集信息可以跳转到详情页（如果需要）
    episodeTextView.setOnClickListener {
      // TODO: 跳转到详情页或选集页面
    }
  }

  override fun getControlViewType(): PSSDK.ControlViewType {
    return PSSDK.ControlViewType.CUSTOM
  }

  override fun bindItemData(shortPlayFragment: ShortPlayFragment, shortPlay: ShortPlay, index: Int) {
    this.shortPlayFragment = shortPlayFragment
    
    // 更新UI数据
    episodeTextView.text = "${shortPlay.total}集 - ${shortPlay.title}"
    titleTextView.text = shortPlay.title
    descTextView.text = shortPlay.desc
  }

  private fun dpToPx(dp: Int): Int {
    return (dp * context.resources.displayMetrics.density).toInt()
  }
}
