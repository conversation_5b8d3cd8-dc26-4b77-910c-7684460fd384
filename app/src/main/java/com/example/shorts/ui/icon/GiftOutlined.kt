package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.GiftOutlined: ImageVector
  get() {
    if (_GiftOutlined != null) {
      return _GiftOutlined!!
    }
    _GiftOutlined = ImageVector.Builder(
      name = "GiftOutlined",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        fill = SolidColor(Color.Black),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(16.684f, 29.169f)
        lineTo(53.316f, 29.169f)
        lineTo(53.316f, 56f)
        curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
        lineTo(19.684f, 59f)
        curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
        lineTo(16.684f, 29.169f)
        lineTo(16.684f, 29.169f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFE42B70),
            1f to Color(0xFFCB0A52)
          ),
          start = Offset(16.684f, 42.906f),
          end = Offset(53.316f, 42.906f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(16.684f, 29.169f)
        lineTo(53.316f, 29.169f)
        lineTo(53.316f, 56f)
        curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
        lineTo(19.684f, 59f)
        curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
        lineTo(16.684f, 29.169f)
        lineTo(16.684f, 29.169f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(16.684f, 29.169f)
          lineTo(53.316f, 29.169f)
          lineTo(53.316f, 56f)
          curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
          lineTo(19.684f, 59f)
          curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
          lineTo(16.684f, 29.169f)
          lineTo(16.684f, 29.169f)
          close()
        }
      ) {
        path(
          fill = Brush.linearGradient(
            colorStops = arrayOf(
              0f to Color(0xFFF35B72),
              1f to Color(0xFFE23852)
            ),
            start = Offset(16.684f, 43.312f),
            end = Offset(35f, 43.312f)
          ),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(16.684f, 29.169f)
          lineTo(35f, 29.169f)
          lineTo(35f, 59f)
          lineTo(19.684f, 59f)
          curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
          lineTo(16.684f, 29.169f)
          lineTo(16.684f, 29.169f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(16.684f, 29.169f)
          lineTo(53.316f, 29.169f)
          lineTo(53.316f, 56f)
          curveTo(53.316f, 57.657f, 51.973f, 59f, 50.316f, 59f)
          lineTo(19.684f, 59f)
          curveTo(18.027f, 59f, 16.684f, 57.657f, 16.684f, 56f)
          lineTo(16.684f, 29.169f)
          lineTo(16.684f, 29.169f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFF6D35E)),
          strokeLineWidth = 1f
        ) {
          moveTo(29.536f, 31.269f)
          lineTo(40.465f, 31.269f)
          lineTo(40.465f, 46.276f)
          curveTo(40.465f, 46.829f, 40.017f, 47.276f, 39.465f, 47.276f)
          curveTo(39.243f, 47.276f, 39.028f, 47.203f, 38.853f, 47.067f)
          lineTo(35f, 44.085f)
          lineTo(35f, 44.085f)
          lineTo(31.148f, 47.067f)
          curveTo(30.711f, 47.405f, 30.083f, 47.325f, 29.745f, 46.889f)
          curveTo(29.61f, 46.713f, 29.536f, 46.498f, 29.536f, 46.276f)
          lineTo(29.536f, 31.269f)
          lineTo(29.536f, 31.269f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(29.536f, 31.269f)
          lineTo(40.465f, 31.269f)
          lineTo(40.465f, 46.276f)
          curveTo(40.465f, 46.829f, 40.017f, 47.276f, 39.465f, 47.276f)
          curveTo(39.243f, 47.276f, 39.028f, 47.203f, 38.853f, 47.067f)
          lineTo(35f, 44.085f)
          lineTo(35f, 44.085f)
          lineTo(31.148f, 47.067f)
          curveTo(30.711f, 47.405f, 30.083f, 47.325f, 29.745f, 46.889f)
          curveTo(29.61f, 46.713f, 29.536f, 46.498f, 29.536f, 46.276f)
          lineTo(29.536f, 31.269f)
          lineTo(29.536f, 31.269f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFE59B53)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(35.245f, 34.702f)
          lineToRelative(-0.245f, 11.049f)
          lineToRelative(5.464f, 2.563f)
          lineToRelative(1.453f, -17.046f)
          close()
        }
      }
      path(
        fill = SolidColor(Color(0xFFF6D35E)),
        strokeLineWidth = 1f
      ) {
        moveTo(23.74f, 13.077f)
        lineTo(23.684f, 13.092f)
        curveTo(20.208f, 14.121f, 18.876f, 17.21f, 19.843f, 20.51f)
        curveTo(20.723f, 23.513f, 23.287f, 25.97f, 26.285f, 26.233f)
        curveTo(30.722f, 26.625f, 32.838f, 26.277f, 34.705f, 25.101f)
        curveTo(36.549f, 23.938f, 36.764f, 22.391f, 36.006f, 19.819f)
        curveTo(34.606f, 15.07f, 30.333f, 11.363f, 23.74f, 13.077f)
        close()
        moveTo(32.836f, 20.656f)
        lineTo(32.991f, 21.198f)
        curveTo(33.229f, 22.083f, 33.17f, 22.224f, 32.88f, 22.406f)
        lineTo(32.642f, 22.547f)
        curveTo(31.51f, 23.172f, 30.048f, 23.353f, 26.531f, 23.044f)
        curveTo(25.04f, 22.912f, 23.541f, 21.477f, 23.014f, 19.678f)
        lineTo(22.958f, 19.47f)
        curveTo(22.565f, 17.898f, 23.078f, 16.722f, 24.501f, 16.217f)
        lineTo(24.665f, 16.163f)
        lineTo(24.638f, 16.171f)
        curveTo(29.118f, 15.007f, 31.876f, 17.4f, 32.836f, 20.656f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(22.266f, 14.327f)
          lineTo(22.216f, 14.355f)
          curveTo(19.092f, 16.195f, 18.547f, 19.514f, 20.283f, 22.482f)
          curveTo(21.864f, 25.183f, 24.946f, 26.947f, 27.919f, 26.477f)
          curveTo(32.319f, 25.783f, 34.288f, 24.934f, 35.815f, 23.341f)
          curveTo(37.323f, 21.767f, 37.157f, 20.214f, 35.799f, 17.902f)
          curveTo(33.292f, 13.632f, 28.249f, 11.069f, 22.266f, 14.327f)
          close()
          moveTo(32.926f, 19.48f)
          lineTo(33.207f, 19.969f)
          curveTo(33.652f, 20.77f, 33.629f, 20.921f, 33.392f, 21.168f)
          lineTo(33.195f, 21.362f)
          curveTo(32.248f, 22.243f, 30.873f, 22.772f, 27.386f, 23.323f)
          curveTo(25.907f, 23.556f, 24.106f, 22.526f, 23.159f, 20.908f)
          lineTo(23.054f, 20.72f)
          curveTo(22.293f, 19.289f, 22.506f, 18.024f, 23.765f, 17.19f)
          lineTo(23.911f, 17.098f)
          lineTo(23.886f, 17.112f)
          curveTo(27.952f, 14.899f, 31.207f, 16.553f, 32.926f, 19.48f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFDFAD42)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(21.432f, 22.194f)
          curveTo(22.753f, 21.394f, 33.338f, 20.522f, 34.609f, 20.522f)
          curveTo(35.457f, 20.522f, 35.457f, 21.566f, 34.609f, 23.653f)
          curveTo(27.618f, 25.351f, 23.811f, 25.643f, 23.191f, 24.526f)
          curveTo(22.26f, 22.851f, 20.111f, 22.995f, 21.432f, 22.194f)
          close()
        }
      }
      path(
        fill = SolidColor(Color(0xFFF6D35E)),
        strokeLineWidth = 1f
      ) {
        moveTo(46.328f, 13.092f)
        curveTo(49.805f, 14.121f, 51.136f, 17.21f, 50.17f, 20.51f)
        curveTo(49.289f, 23.513f, 46.726f, 25.97f, 43.727f, 26.233f)
        curveTo(39.29f, 26.625f, 37.174f, 26.277f, 35.307f, 25.101f)
        curveTo(33.463f, 23.938f, 33.249f, 22.391f, 34.007f, 19.819f)
        curveTo(35.382f, 15.152f, 39.533f, 11.491f, 45.934f, 12.992f)
        lineTo(46.328f, 13.092f)
        close()
        moveTo(37.176f, 20.656f)
        lineTo(37.022f, 21.198f)
        curveTo(36.798f, 22.031f, 36.837f, 22.205f, 37.084f, 22.374f)
        lineTo(37.132f, 22.406f)
        curveTo(38.305f, 23.144f, 39.714f, 23.376f, 43.481f, 23.044f)
        curveTo(44.973f, 22.912f, 46.471f, 21.477f, 46.999f, 19.678f)
        lineTo(47.055f, 19.47f)
        curveTo(47.447f, 17.898f, 46.935f, 16.722f, 45.511f, 16.217f)
        lineTo(45.342f, 16.163f)
        lineTo(45.375f, 16.171f)
        curveTo(40.894f, 15.007f, 38.136f, 17.4f, 37.176f, 20.656f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(36.038f, 14.355f)
          curveTo(32.914f, 16.195f, 32.369f, 19.514f, 34.105f, 22.482f)
          curveTo(35.686f, 25.183f, 38.768f, 26.947f, 41.741f, 26.477f)
          curveTo(46.141f, 25.783f, 48.11f, 24.934f, 49.637f, 23.341f)
          curveTo(51.145f, 21.767f, 50.979f, 20.214f, 49.621f, 17.902f)
          curveTo(47.158f, 13.706f, 42.244f, 11.158f, 36.396f, 14.163f)
          lineTo(36.038f, 14.355f)
          close()
          moveTo(46.748f, 19.48f)
          lineTo(47.029f, 19.969f)
          curveTo(47.448f, 20.723f, 47.452f, 20.901f, 47.253f, 21.125f)
          lineTo(47.214f, 21.168f)
          curveTo(46.255f, 22.168f, 44.944f, 22.733f, 41.208f, 23.323f)
          curveTo(39.729f, 23.556f, 37.928f, 22.526f, 36.981f, 20.908f)
          lineTo(36.876f, 20.72f)
          curveTo(36.115f, 19.289f, 36.328f, 18.024f, 37.587f, 17.19f)
          lineTo(37.738f, 17.096f)
          lineTo(37.708f, 17.112f)
          curveTo(41.774f, 14.899f, 45.029f, 16.553f, 46.748f, 19.48f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFDF8F42)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(33.749f, 22.273f)
          curveTo(34.423f, 21.865f, 38.461f, 22.822f, 42.314f, 22.273f)
          curveTo(46.015f, 21.746f, 49.538f, 19.703f, 50.161f, 19.703f)
          curveTo(51.432f, 19.703f, 52.157f, 22.851f, 48.151f, 23.487f)
          curveTo(44.145f, 24.124f, 37.909f, 25.794f, 36.978f, 24.12f)
          curveTo(36.048f, 22.445f, 32.428f, 23.074f, 33.749f, 22.273f)
          close()
        }
      }
      path(
        fill = SolidColor(Color.Black),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(14.5f, 22.641f)
        lineTo(55.5f, 22.641f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 57.5f, 24.641f)
        lineTo(57.5f, 32.702f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 55.5f, 34.702f)
        lineTo(14.5f, 34.702f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.5f, 32.702f)
        lineTo(12.5f, 24.641f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.5f, 22.641f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFE42B70),
            1f to Color(0xFFCB0A52)
          ),
          start = Offset(12.5f, 28.195f),
          end = Offset(57.5f, 28.195f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(14.5f, 22.641f)
        lineTo(55.5f, 22.641f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 57.5f, 24.641f)
        lineTo(57.5f, 32.702f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 55.5f, 34.702f)
        lineTo(14.5f, 34.702f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.5f, 32.702f)
        lineTo(12.5f, 24.641f)
        arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.5f, 22.641f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(14.5f, 22.641f)
          lineTo(55.5f, 22.641f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 57.5f, 24.641f)
          lineTo(57.5f, 32.702f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 55.5f, 34.702f)
          lineTo(14.5f, 34.702f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.5f, 32.702f)
          lineTo(12.5f, 24.641f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.5f, 22.641f)
          close()
        }
      ) {
        path(
          fill = Brush.linearGradient(
            colorStops = arrayOf(
              0f to Color(0x99FB9A5A),
              1f to Color(0xFFF28430)
            ),
            start = Offset(12.211f, 27.714f),
            end = Offset(41.932f, 27.714f)
          ),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(48.151f, 21.414f)
          lineToRelative(-10.713f, 13.289f)
          lineToRelative(-27.975f, 0f)
          lineToRelative(0f, -13.289f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(14.5f, 22.641f)
          lineTo(55.5f, 22.641f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 57.5f, 24.641f)
          lineTo(57.5f, 32.702f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 55.5f, 34.702f)
          lineTo(14.5f, 34.702f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.5f, 32.702f)
          lineTo(12.5f, 24.641f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.5f, 22.641f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFF6D35E)),
          strokeLineWidth = 1f
        ) {
          moveTo(28.807f, 22.641f)
          horizontalLineToRelative(12.331f)
          verticalLineToRelative(12.359f)
          horizontalLineToRelative(-12.331f)
          close()
        }
      }
      group(
        clipPathData = PathData {
          moveTo(14.5f, 22.641f)
          lineTo(55.5f, 22.641f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 57.5f, 24.641f)
          lineTo(57.5f, 32.702f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 55.5f, 34.702f)
          lineTo(14.5f, 34.702f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 12.5f, 32.702f)
          lineTo(12.5f, 24.641f)
          arcTo(2f, 2f, 0f, isMoreThanHalf = false, isPositiveArc = true, 14.5f, 22.641f)
          close()
        }
      ) {
        path(
          fill = SolidColor(Color(0xFFE59B53)),
          strokeLineWidth = 1f,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(35f, 22.641f)
          horizontalLineToRelative(6.138f)
          verticalLineToRelative(12.359f)
          horizontalLineToRelative(-6.138f)
          close()
        }
      }
    }.build()

    return _GiftOutlined!!
  }

@Suppress("ObjectPropertyName")
private var _GiftOutlined: ImageVector? = null

@Preview
@Composable
private fun GiftOutlinedPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.GiftOutlined, contentDescription = null)
  }
}
