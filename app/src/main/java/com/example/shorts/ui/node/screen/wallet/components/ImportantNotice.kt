package com.example.shorts.ui.node.screen.wallet.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.ui.theme.AppTheme

/**
 * 重要提示区域组件
 */
@Composable
fun ImportantNotice(
  modifier: Modifier = Modifier
) {
  val strings = LocalStrings.current

  Column(
    modifier = modifier
  ) {
    // 标题
    Text(
      text = strings.importantNotice,
      fontSize = 15.sp,
      fontWeight = FontWeight.Medium,
      color = Color.White,
      modifier = Modifier.padding(bottom = 12.dp)
    )

    // 提示内容列表
    val noticeItems = listOf(
      strings.noticeItem1,
      strings.noticeItem2,
      strings.noticeItem3,
      strings.noticeItem4,
      strings.noticeItem5,
      strings.noticeItem6,
      strings.noticeItem7
    )

    Column(
      verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
      noticeItems.forEach { item ->
        NoticeItem(text = item)
      }
    }
  }
}

/**
 * 单个提示项
 */
@Composable
private fun NoticeItem(
  text: String,
  modifier: Modifier = Modifier
) {
  Text(
    text = text,
    fontSize = 12.sp,
    color = Color.White.copy(alpha = 0.8f),
    lineHeight = 16.sp,
    modifier = modifier.fillMaxWidth()
  )
}

@Preview
@Composable
fun ImportantNoticePreview() {
  AppTheme {
    Box(
      modifier = Modifier
        .fillMaxWidth()
        .padding(16.dp)
    ) {
      ImportantNotice()
    }
  }
}
