package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.Fav: ImageVector
  get() {
    if (_Fav != null) {
      return _Fav!!
    }
    _Fav = ImageVector.Builder(
      name = "Fav",
      defaultWidth = 48.dp,
      defaultHeight = 48.dp,
      viewportWidth = 48f,
      viewportHeight = 48f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(32.935f, 8.704f)
        curveTo(37.706f, 8.704f, 42.125f, 13.429f, 42.125f, 19.816f)
        curveTo(42.125f, 26.203f, 38.077f, 29.829f, 35.579f, 32.438f)
        curveTo(33.082f, 35.046f, 29.846f, 37.82f, 27.52f, 39.704f)
        curveTo(25.194f, 41.587f, 22.661f, 41.587f, 20.341f, 39.704f)
        curveTo(18.02f, 37.82f, 14.991f, 35.212f, 12.598f, 32.438f)
        curveTo(10.206f, 29.664f, 5.875f, 25.953f, 5.875f, 19.816f)
        curveTo(5.875f, 13.679f, 11.738f, 8.704f, 15.706f, 8.704f)
        curveTo(19.673f, 8.704f, 20.809f, 10.24f, 21.829f, 11.049f)
        curveTo(22.3f, 11.423f, 22.792f, 11.853f, 23.306f, 12.34f)
        lineTo(23.305f, 12.341f)
        curveTo(23.693f, 12.707f, 24.3f, 12.704f, 24.684f, 12.335f)
        curveTo(25.184f, 11.851f, 25.673f, 11.422f, 26.151f, 11.049f)
        curveTo(27.192f, 10.235f, 28.163f, 8.704f, 32.935f, 8.704f)
        close()
      }
    }.build()

    return _Fav!!
  }

@Suppress("ObjectPropertyName")
private var _Fav: ImageVector? = null

@Preview
@Composable
private fun FavPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.Fav, contentDescription = null)
  }
}
