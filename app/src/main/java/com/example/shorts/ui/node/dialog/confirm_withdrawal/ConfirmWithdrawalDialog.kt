package com.example.shorts.ui.node.dialog.confirm_withdrawal

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AccountCircle
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.R
import com.example.shorts.foundation.guia.DialogNode
import com.example.shorts.foundation.mmkv.WalletKvStore
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.common.dialog.loading.LoadingDialogContent
import com.example.shorts.ui.node.screen.wallet.WalletSideEffect
import com.example.shorts.ui.node.screen.wallet.WalletUiModel
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.ui.theme.AppTheme
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.pop
import com.roudikk.guia.extensions.replaceLast
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject
import org.orbitmvi.orbit.compose.collectAsState
import org.orbitmvi.orbit.compose.collectSideEffect
import java.math.BigDecimal

private const val TAG = "ConfirmWithdrawalDialog"

@Parcelize
class ConfirmWithdrawalDialogNode(
  private val withdrawal: Withdrawal,
  @IgnoredOnParcel private val walletUiModel: WalletUiModel? = null
) : DialogNode("confirm_withdrawal") {

  @Composable
  override fun Content(
    navigator: Navigator,
    dialog: Dialog?
  ) {
    val walletKvStore: WalletKvStore = koinInject()
    val walletAddress by walletKvStore.walletAddress.collectAsState()

    val walletUiModel = walletUiModel ?: koinInject()
    val walletUiState by walletUiModel.collectAsState()

    walletUiModel.collectSideEffect {
      when (it) {
        is WalletSideEffect.WithdrawSuccessful -> {
          navigator.replaceLast(ConfirmWithdrawalSuccessfulDialogNode(it.withdrawal))
        }
      }
    }

    if (walletUiState.isLoading) {
      LoadingDialogContent()
    } else {
      ConfirmWithdrawalDialog(
        onDismiss = navigator::pop,
        onConfirm = { walletUiModel.onPerformWithdraw(withdrawal) },
        withdrawal = withdrawal,
        walletAddress = walletAddress
      )
    }
  }

}

@Composable
fun ConfirmWithdrawalDialog(
  onDismiss: () -> Unit,
  onConfirm: () -> Unit,
  withdrawal: Withdrawal,
  walletAddress: String
) {
  val strings = LocalStrings.current

  Dialog(
    onDismissRequest = onDismiss,
  ) {
    Card(
      modifier = Modifier.fillMaxWidth(),
      shape = RoundedCornerShape(16.dp),
      colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
      Column(
        modifier = Modifier
          .fillMaxWidth()
          .padding(vertical = 24.dp, horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
      ) {
        // Header with close button
        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.SpaceBetween,
          verticalAlignment = Alignment.CenterVertically
        ) {
          Spacer(modifier = Modifier.width(24.dp))

          Text(
            text = strings.confirmWithdrawal,
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium
          )

          IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(24.dp)
          ) {
            Icon(
              imageVector = Icons.Rounded.Close,
              contentDescription = "Close",
              tint = Color.White.copy(alpha = 0.7f)
            )
          }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Payment method display
        Box(
          modifier = Modifier
            .fillMaxWidth()
            .background(
              Color.White,
              RoundedCornerShape(12.dp)
            )
            .padding(16.dp),
          contentAlignment = Alignment.Center
        ) {
          Image(
            painter = painterResource(id = R.drawable.img_paypal),
            contentDescription = "PayPal",
            modifier = Modifier
              .height(48.dp)
              .alpha(.1f),
            contentScale = ContentScale.Crop
          )

          Icon(
            imageVector = Icons.Rounded.AccountCircle,
            contentDescription = null,
            modifier = Modifier
              .size(48.dp)
              .clip(CircleShape)
              .background(MaterialTheme.colorScheme.primary),
            tint = Color.White
          )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Withdrawal details
        Text(
          text = strings.withdrawalConfirmationText(
            withdrawal.displayAmount,
            walletAddress
          ),
          color = Color.White.copy(alpha = 0.8f),
          fontSize = 13.sp,
          lineHeight = 18.sp,
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Action buttons
        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
          // Back button
          GradientButton(
            text = strings.back,
            onClick = onDismiss,
            modifier = Modifier
              .weight(1f)
              .height(48.dp),
            gradient = GradientColors.Negative,
            shape = CircleShape
          )

          // Confirm button
          GradientButton(
            text = strings.confirm,
            onClick = onConfirm,
            modifier = Modifier
              .weight(1f)
              .height(48.dp),
            gradient = GradientColors.Purple,
            shape = CircleShape
          )
        }
      }
    }
  }
}

@Preview
@Composable
fun ConfirmWithdrawalDialogPreview() {
  AppTheme {
    ConfirmWithdrawalDialog(
      onDismiss = { },
      onConfirm = { },
      withdrawal = Withdrawal(BigDecimal("123.45"), "$"),
      walletAddress = "<EMAIL>"
    )
  }
}