package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.Search: ImageVector
  get() {
    if (_Search != null) {
      return _Search!!
    }
    _Search = ImageVector.Builder(
      name = "Search",
      defaultWidth = 52.dp,
      defaultHeight = 52.dp,
      viewportWidth = 52f,
      viewportHeight = 52f
    ).apply {
      path(
        stroke = SolidColor(Color.White),
        strokeLineWidth = 4f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(23.759f, 23.612f)
        moveToRelative(-16.802f, 0f)
        arcToRelative(16.802f, 16.802f, 0f, isMoreThanHalf = true, isPositiveArc = true, 33.605f, 0f)
        arcToRelative(16.802f, 16.802f, 0f, isMoreThanHalf = true, isPositiveArc = true, -33.605f, 0f)
      }
      path(
        stroke = SolidColor(Color.White),
        strokeLineWidth = 4f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(45.043f, 45.19f)
        lineTo(36.108f, 36.16f)
      }
    }.build()

    return _Search!!
  }

@Suppress("ObjectPropertyName")
private var _Search: ImageVector? = null

@Preview
@Composable
private fun SearchPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.Search, contentDescription = null)
  }
}
