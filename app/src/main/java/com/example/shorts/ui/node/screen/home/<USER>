package com.example.shorts.ui.node.screen.home

import com.example.shorts.foundation.mvi_ui_model.UiModel
import org.koin.android.annotation.KoinViewModel
import org.koin.core.annotation.InjectedParam

@KoinViewModel
class HomeUiModel(
  @InjectedParam initialTab: HomeTab = HomeTab.Shorts
) : UiModel<HomeUiState, Nothing>(
  state = HomeUiState(currentTab = initialTab)
) {

  fun selectTab(tab: HomeTab) = intent {
    reduce { state.copy(currentTab = tab) }
  }

}