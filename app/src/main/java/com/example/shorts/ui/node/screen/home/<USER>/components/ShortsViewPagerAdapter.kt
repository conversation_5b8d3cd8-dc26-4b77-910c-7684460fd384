package com.example.shorts.ui.node.screen.home.shorts.components

import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.GradientDrawable
import android.view.Gravity
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.adapter.FragmentViewHolder
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.bytedance.sdk.shortplay.api.ShortPlayFragment
import com.example.shorts.R
import com.example.shorts.foundation.guia.GlobalNavigator
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.lyricist.globalAppStrings
import com.example.shorts.ui.node.screen.home.HomeNode
import com.example.shorts.ui.node.screen.home.shorts.EpisodeSelectionBottomSheetNode
import com.example.shorts.ui.node.screen.home.shorts.DramaPlayScreenNode
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.push

class ShortsViewPagerAdapter(
  private val activity: FragmentActivity,
  private var shortPlays: List<ShortPlay>,
  private var onPageSelected: (Int) -> Unit
) : FragmentStateAdapter(activity), DefaultLifecycleObserver {

  private val fragmentMap = HashMap<Int, ShortPlayFragment>()
  private var currentPosition = 0

  fun getFragmentByPosition(position: Int): ShortPlayFragment? {
    return fragmentMap[position]
  }

  fun getCurrentFragment(): ShortPlayFragment? {
    return fragmentMap[currentPosition]
  }

  fun setShortPlays(newShortPlays: List<ShortPlay>) {
    if (shortPlays == newShortPlays) return

    shortPlays = newShortPlays
    notifyDataSetChanged()
  }

  fun addShortPlays(newShortPlays: List<ShortPlay>) {
    shortPlays = shortPlays + newShortPlays
    notifyItemRangeInserted(shortPlays.size - newShortPlays.size, newShortPlays.size)
  }

  override fun onDestroy(owner: LifecycleOwner) {
    for (entry in fragmentMap.entries) {
      if (entry.value == owner) {
        fragmentMap.remove(entry.key)
        break
      }
    }
  }

  override fun onPause(owner: LifecycleOwner) {
    debugLog(tag = "ShortsViewPagerAdapter") { "onPause $this" }
  }

  override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
    super.onDetachedFromRecyclerView(recyclerView)
    debugLog(tag = "ShortsViewPagerAdapter") { "onDetachedFromRecyclerView $this" }
  }

  override fun onViewDetachedFromWindow(holder: FragmentViewHolder) {
    super.onViewDetachedFromWindow(holder)
    debugLog(tag = "ShortsViewPagerAdapter") { "onViewDetachedFromWindow $this" }
  }

  override fun createFragment(position: Int): Fragment {
    val shortPlay = shortPlays[position]

    // 配置播放器
    val builder = PSSDK.DetailPageConfig.Builder()
    builder
      .hideLeftTopCloseAndTitle(true, null) // 隐藏顶部标题栏
      .displayBottomExtraView(false) // 不显示底部额外视图
      .displayProgressBar(true)
      .progressBarMarginToBottom(8)
      .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_TITLE, false) // 隐藏底部标题
      .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_DESC, false) // 隐藏底部描述
      .playSingleItem(true) // 只播放单个视频，不允许滑动切换
      .enableImmersiveMode(0) // 禁用沉浸式模式

    val detailFragment = PSSDK.createDetailFragment(
      shortPlay,
      builder.build(),
      object : PSSDK.ShortPlayDetailPageListener {
        override fun onShortPlayPlayed(shortPlay: ShortPlay, index: Int) {
          debugLog(tag = "ShortsViewPagerAdapter") { "Playing episode $index of ${shortPlay.title}" }
          currentPosition = position
          onPageSelected(position)
        }

        override fun onOverScroll(direction: Int) {
          // Feed模式下不处理过度滚动
        }

        override fun onProgressChange(
          shortPlay: ShortPlay,
          index: Int,
          currentPlayTime: Int,
          duration: Int
        ) {
          // 可以在这里处理播放进度
        }

        override fun onPlayFailed(errorInfo: PSSDK.ErrorInfo): Boolean {
          debugLog(tag = "ShortsViewPagerAdapter") { "Play failed: ${errorInfo.code} - ${errorInfo.msg}" }
          return false
        }

        override fun onItemSelected(
          position: Int,
          type: PSSDK.ShortPlayDetailPageListener.ItemType,
          index: Int
        ) {
          // Feed模式下不需要处理
        }

        override fun onVideoPlayStateChanged(shortPlay: ShortPlay, index: Int, playbackState: Int) {
          debugLog(tag = "ShortsViewPagerAdapter") { "Play state changed: $playbackState" }
          if (playbackState == PSSDK.PLAYBACK_STATE_PLAY && GlobalNavigator.navigator?.currentKey !is HomeNode) {
            getCurrentFragment()?.pausePlay()
            debugLog(tag = "ShortsViewPagerAdapter") { "Paused play on currentKey not HomeNode" }
          }
        }

        override fun onVideoPlayCompleted(shortPlay: ShortPlay, index: Int) {
          debugLog(tag = "ShortsViewPagerAdapter") { "Video completed: episode $index" }
        }

        override fun onEnterImmersiveMode() {
          // Feed模式下不使用沉浸式模式
        }

        override fun onExitImmersiveMode() {
          // Feed模式下不使用沉浸式模式
        }

        override fun isNeedBlock(shortPlay: ShortPlay, index: Int): Boolean {
          // Feed模式下暂时不实现解锁逻辑
          return false
        }

        override fun showAdIfNeed(
          shortPlay: ShortPlay,
          index: Int,
          listener: PSSDK.ShortPlayBlockResultListener
        ) {
          listener.onShortPlayUnlocked()
        }

        override fun onVideoInfoFetched(
          shortPlay: ShortPlay,
          index: Int,
          videoPlayInfo: PSSDK.VideoPlayInfo
        ) {
          debugLog(tag = "ShortsViewPagerAdapter") { "Video info fetched for episode $index" }
        }

        override fun onObtainPlayerControlViews(): List<View>? {
          debugLog(tag = "ShortsViewPagerAdapter") { "onObtainPlayerControlViews called for ${shortPlay.title}" }
          val views = ArrayList<View>()

          // 右侧按钮组容器
          val rightButtonContainer = createRightButtonContainer(shortPlay)
          views.add(rightButtonContainer)
          debugLog(tag = "ShortsViewPagerAdapter") { "Added right button container" }

          // 底部信息区域容器
          val bottomInfoContainer = createBottomInfoContainer(shortPlay)
          views.add(bottomInfoContainer)
          debugLog(tag = "ShortsViewPagerAdapter") { "Added bottom info container" }

          debugLog(tag = "ShortsViewPagerAdapter") { "Returning ${views.size} overlay views" }
          return views
        }
      })

    if (detailFragment != null) {
      detailFragment.lifecycle.addObserver(this)
      fragmentMap[position] = detailFragment
    }
    return detailFragment ?: Fragment()
  }

  override fun getItemCount(): Int = shortPlays.size

  /**
   * 创建右侧按钮组容器
   */
  private fun createRightButtonContainer(shortPlay: ShortPlay): View {
    debugLog(tag = "ShortsViewPagerAdapter") { "Creating right button container for ${shortPlay.title}" }

    // 创建实现IControlView接口的自定义容器
    val container = object : LinearLayout(activity), PSSDK.IControlView {
      override fun getControlViewType(): PSSDK.ControlViewType {
        return PSSDK.ControlViewType.CUSTOM
      }

      override fun bindItemData(fragment: ShortPlayFragment?, shortPlay: ShortPlay?, index: Int) {
        // 不需要特殊处理
      }
    }.apply {
      orientation = LinearLayout.VERTICAL

      val params = FrameLayout.LayoutParams(
        FrameLayout.LayoutParams.WRAP_CONTENT,
        FrameLayout.LayoutParams.WRAP_CONTENT
      )
      params.gravity = Gravity.END or Gravity.BOTTOM
      params.rightMargin = dpToPx(16)
      params.bottomMargin = dpToPx(120)
      layoutParams = params
    }

    // 点赞按钮
    val likeButton = createActionButton(
      iconRes = android.R.drawable.btn_star_big_on,
      onClick = {
        debugLog(tag = "ShortsViewPagerAdapter") { "Like clicked for ${shortPlay.title}" }
        // TODO: 实现点赞逻辑
      }
    )

    // 收藏按钮
    val collectButton = createActionButton(
      iconRes = android.R.drawable.btn_star,
      onClick = {
        debugLog(tag = "ShortsViewPagerAdapter") { "Collect clicked for ${shortPlay.title}" }
        // TODO: 实现收藏逻辑
      }
    )

    // Episodes按钮
    val episodesButton = createEpisodesButton(shortPlay) {
      debugLog(tag = "ShortsViewPagerAdapter") { "Episodes clicked for ${shortPlay.title}" }
      // 显示剧集选择弹窗
      GlobalNavigator.tryTransaction {
        push(
          EpisodeSelectionBottomSheetNode(
            shortPlay = shortPlay,
            currentEpisode = 1, // 默认从第1集开始
            onEpisodeSelected = { episode ->
              // 跳转到专用播放页面
              GlobalNavigator.tryTransaction {
                push(
                  DramaPlayScreenNode(
                    shortPlay = shortPlay,
                    startEpisode = episode,
                    startFromSeconds = 0
                  )
                )
              }
            }
          )
        )
      }
    }

    // 添加按钮到容器，设置间距
//    container.addView(likeButton)
//    container.addView(collectButton)
    container.addView(episodesButton)

    // 设置按钮间距
    val buttonMargin = dpToPx(12)
    for (i in 0 until container.childCount) {
      val child = container.getChildAt(i)
      val params = child.layoutParams as LinearLayout.LayoutParams
      if (i > 0) {
        params.topMargin = buttonMargin
      }
    }

    return container
  }

  /**
   * 创建单个操作按钮
   */
  private fun createActionButton(iconRes: Int, onClick: () -> Unit): View {
    return CustomActionButton(activity).apply {
      setImageResource(iconRes)
      val params = LinearLayout.LayoutParams(dpToPx(48), dpToPx(48))
      layoutParams = params
      setOnClickListener { onClick() }
    }
  }

  /**
   * 创建Episodes按钮
   */
  private fun createEpisodesButton(shortPlay: ShortPlay, onClick: () -> Unit): View {
    return LinearLayout(activity).apply {
      orientation = LinearLayout.VERTICAL
      gravity = Gravity.CENTER

      val params = LinearLayout.LayoutParams(dpToPx(56), dpToPx(60))
      layoutParams = params

      // 设置背景
      val background = GradientDrawable().apply {
        shape = GradientDrawable.RECTANGLE
        cornerRadius = dpToPx(8).toFloat()
        setColor(Color.parseColor("#80000000")) // 半透明黑色
      }
      setBackground(background)

      // Episodes图标
      val iconView = ImageView(activity).apply {
        setImageResource(R.drawable.ic_episodes) // 使用列表图标
        setColorFilter(Color.WHITE)
        val iconParams = LinearLayout.LayoutParams(dpToPx(24), dpToPx(24))
        iconParams.bottomMargin = dpToPx(2)
        layoutParams = iconParams
      }

      // Episodes文字
      val textView = TextView(activity).apply {
        text = globalAppStrings.episodes
        textSize = 9.5f
        setTextColor(Color.WHITE)
        gravity = Gravity.CENTER
        val textParams = LinearLayout.LayoutParams(
          LinearLayout.LayoutParams.WRAP_CONTENT,
          LinearLayout.LayoutParams.WRAP_CONTENT
        )
        layoutParams = textParams
      }

      addView(iconView)
      addView(textView)

      // 设置点击效果
      isClickable = true
      isFocusable = true
      setOnClickListener { onClick() }

      // 添加点击动画效果
      setOnTouchListener { v, event ->
        when (event.action) {
          android.view.MotionEvent.ACTION_DOWN -> {
            animate().scaleX(0.9f).scaleY(0.9f).setDuration(100).start()
          }
          android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
            animate().scaleX(1.0f).scaleY(1.0f).setDuration(100).start()
          }
        }
        false
      }
    }
  }

  /**
   * 创建底部信息区域容器
   */
  private fun createBottomInfoContainer(shortPlay: ShortPlay): View {
    debugLog(tag = "ShortsViewPagerAdapter") { "Creating bottom info container for ${shortPlay.title}" }

    // 创建实现IControlView接口的自定义容器
    val container = object : LinearLayout(activity), PSSDK.IControlView {
      override fun getControlViewType(): PSSDK.ControlViewType {
        return PSSDK.ControlViewType.CUSTOM
      }

      override fun bindItemData(fragment: ShortPlayFragment?, shortPlay: ShortPlay?, index: Int) {
        // 不需要特殊处理
      }
    }.apply {
      orientation = LinearLayout.VERTICAL

      val params = FrameLayout.LayoutParams(
        FrameLayout.LayoutParams.MATCH_PARENT,
        FrameLayout.LayoutParams.WRAP_CONTENT
      )
      params.gravity = Gravity.BOTTOM
      params.leftMargin = dpToPx(16)
      params.rightMargin = dpToPx(80) // 为右侧按钮留出空间
      params.bottomMargin = dpToPx(40) // 增加底部边距
      layoutParams = params
    }

    // 标题
    val titleView = TextView(activity).apply {
      text = shortPlay.title
      textSize = 18f
      setTextColor(Color.WHITE)
      typeface = Typeface.DEFAULT_BOLD
      setShadowLayer(2f, 1f, 1f, Color.BLACK)
      val params = LinearLayout.LayoutParams(
        LinearLayout.LayoutParams.MATCH_PARENT,
        LinearLayout.LayoutParams.WRAP_CONTENT
      )
      params.bottomMargin = dpToPx(8)
      layoutParams = params
    }

    // 描述文字（支持展开/收起）
    val descView = createExpandableDescView(shortPlay)

    container.addView(titleView)
    container.addView(descView)

    return container
  }

  /**
   * 创建可展开/收起的描述文字
   */
  private fun createExpandableDescView(shortPlay: ShortPlay): View {
    val container = LinearLayout(activity).apply {
      orientation = LinearLayout.HORIZONTAL
      val params = LinearLayout.LayoutParams(
        LinearLayout.LayoutParams.MATCH_PARENT,
        LinearLayout.LayoutParams.WRAP_CONTENT
      )
      layoutParams = params
    }

    val descView = TextView(activity).apply {
      text = shortPlay.desc
      textSize = 14f
      setTextColor(Color.WHITE)
      setShadowLayer(2f, 1f, 1f, Color.BLACK)
      maxLines = 2 // 初始状态显示2行

      val params = LinearLayout.LayoutParams(
        0,
        LinearLayout.LayoutParams.WRAP_CONTENT,
        1f
      )
      layoutParams = params
    }

    val arrowView = ImageView(activity).apply {
      setImageResource(com.example.shorts.R.drawable.ic_arrow_down)

      val params = LinearLayout.LayoutParams(
        dpToPx(16), // 设置固定宽度
        dpToPx(16)  // 设置固定高度
      )
      params.gravity = Gravity.BOTTOM
      params.leftMargin = dpToPx(8) // 与文字之间的间距
      layoutParams = params
    }

    var isExpanded = false

    val clickListener = View.OnClickListener {
      isExpanded = !isExpanded
      if (isExpanded) {
        descView.maxLines = Integer.MAX_VALUE
        arrowView.rotation = 180f // 旋转180度，变成向上箭头
      } else {
        descView.maxLines = 2
        arrowView.rotation = 0f // 恢复原始状态，向下箭头
      }
      debugLog(tag = "ShortsViewPagerAdapter") {
        "Description ${if (isExpanded) "expanded" else "collapsed"} for ${shortPlay.title}"
      }
    }

    descView.setOnClickListener(clickListener)
    arrowView.setOnClickListener(clickListener)

    container.addView(descView)
    container.addView(arrowView)

    return container
  }

  private fun dpToPx(dp: Int): Int {
    return (dp * activity.resources.displayMetrics.density).toInt()
  }

  /**
   * 更新页面选中回调
   */
  fun updatePageSelectedCallback(callback: (Int) -> Unit) {
    onPageSelected = callback
  }

  /**
   * 获取当前播放位置
   */
  fun getCurrentPosition(): Int = currentPosition

  /**
   * 暂停所有视频播放
   */
  fun pauseAllVideos() {
    debugLog(tag = "ShortsViewPagerAdapter") { "Pausing all videos" }
    fragmentMap.values.forEach { fragment ->
      try {
        // 使用PSSDK的pausePlay方法
        fragment.pausePlay()
      } catch (e: Exception) {
        debugLog(tag = "ShortsViewPagerAdapter") { "Error pausing fragment: ${e.message}" }
      }
    }
  }

  fun resumeAllVideos() {
    debugLog(tag = "ShortsViewPagerAdapter") { "Pausing all videos" }
    fragmentMap.values.forEach { fragment ->
      try {
        // 使用PSSDK的pausePlay方法
        fragment.startPlay()
      } catch (e: Exception) {
        debugLog(tag = "ShortsViewPagerAdapter") { "Error pausing fragment: ${e.message}" }
      }
    }
  }

  /**
   * 恢复当前视频播放
   */
  fun resumeCurrentVideo() {
    debugLog(tag = "ShortsViewPagerAdapter") { "Resuming current video at position: $currentPosition" }
    fragmentMap[currentPosition]?.let { fragment ->
      try {
        // 使用PSSDK的startPlay方法
        fragment.startPlay()
      } catch (e: Exception) {
        debugLog(tag = "ShortsViewPagerAdapter") { "Error resuming fragment: ${e.message}" }
      }
    }
  }

  /**
   * 停止所有视频播放并释放资源
   */
  fun destroy() {
    debugLog(tag = "ShortsViewPagerAdapter") { "Destroying adapter and releasing video resources" }

    // 停止所有视频播放
    fragmentMap.values.forEach { fragment ->
      try {
        fragment.pausePlay()
        fragment.lifecycle.removeObserver(this)
      } catch (e: Exception) {
        debugLog(tag = "ShortsViewPagerAdapter") { "Error stopping fragment: ${e.message}" }
      }
    }

    fragmentMap.clear()
  }

  fun setMutedCurrentVideo(muted: Boolean) {
    debugLog(tag = "ShortsViewPagerAdapter") { "Muting current video at position: $currentPosition" }
    fragmentMap.values.forEach { fragment ->
      try {
        fragment.setMuted(muted)
      } catch (e: Exception) {
        debugLog(tag = "ShortsViewPagerAdapter") { "Error muting fragment: ${e.message}" }
      }
    }
  }
}
