package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.AdIcon: ImageVector
  get() {
    if (_AdIcon != null) {
      return _AdIcon!!
    }
    _AdIcon = ImageVector.Builder(
      name = "AdIcon",
      defaultWidth = 78.dp,
      defaultHeight = 78.dp,
      viewportWidth = 78f,
      viewportHeight = 78f
    ).apply {
      path(
        fill = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(10f, 0f)
        lineTo(68f, 0f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 78f, 10f)
        lineTo(78f, 68f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 68f, 78f)
        lineTo(10f, 78f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 0f, 68f)
        lineTo(0f, 10f)
        arcTo(10f, 10f, 0f, isMoreThanHalf = false, isPositiveArc = true, 10f, 0f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color.White,
            1f to Color(0xFFE1E2E4)
          ),
          start = Offset(32.923f, 54.46f),
          end = Offset(15.797f, 54.46f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(14.07f, 51.359f)
        curveTo(14.07f, 50.992f, 14.201f, 50.681f, 14.463f, 50.426f)
        curveTo(14.724f, 50.172f, 15.039f, 50.045f, 15.407f, 50.045f)
        lineTo(16.743f, 50.045f)
        lineTo(16.743f, 56.004f)
        lineTo(15.407f, 56.004f)
        curveTo(15.039f, 56.004f, 14.724f, 55.877f, 14.463f, 55.623f)
        curveTo(14.201f, 55.368f, 14.07f, 55.057f, 14.07f, 54.689f)
        lineTo(14.07f, 51.359f)
        close()
        moveTo(28.917f, 45.888f)
        curveTo(29.525f, 45.308f, 30.076f, 44.99f, 30.571f, 44.933f)
        curveTo(31.066f, 44.877f, 31.504f, 45.007f, 31.886f, 45.326f)
        curveTo(32.268f, 45.644f, 32.593f, 46.11f, 32.861f, 46.725f)
        curveTo(33.13f, 47.34f, 33.349f, 48.026f, 33.519f, 48.783f)
        curveTo(33.689f, 49.539f, 33.816f, 50.331f, 33.901f, 51.158f)
        curveTo(33.986f, 51.985f, 34.028f, 52.773f, 34.028f, 53.523f)
        curveTo(34.028f, 54.272f, 33.975f, 55.022f, 33.869f, 55.771f)
        curveTo(33.763f, 56.52f, 33.607f, 57.224f, 33.402f, 57.881f)
        curveTo(33.197f, 58.539f, 32.957f, 59.115f, 32.681f, 59.61f)
        curveTo(32.405f, 60.105f, 32.098f, 60.472f, 31.759f, 60.713f)
        curveTo(31.419f, 60.953f, 31.055f, 61.038f, 30.666f, 60.967f)
        curveTo(30.278f, 60.896f, 29.871f, 60.621f, 29.447f, 60.14f)
        curveTo(29.037f, 59.659f, 28.482f, 59.26f, 27.782f, 58.942f)
        curveTo(27.082f, 58.624f, 26.319f, 58.355f, 25.491f, 58.136f)
        curveTo(24.664f, 57.917f, 23.827f, 57.74f, 22.978f, 57.606f)
        curveTo(22.13f, 57.471f, 21.352f, 57.351f, 20.645f, 57.245f)
        curveTo(19.938f, 57.139f, 19.348f, 57.036f, 18.874f, 56.937f)
        curveTo(18.401f, 56.838f, 18.135f, 56.718f, 18.079f, 56.577f)
        lineTo(18.079f, 49.938f)
        curveTo(18.135f, 49.741f, 18.358f, 49.578f, 18.747f, 49.451f)
        curveTo(19.136f, 49.323f, 19.631f, 49.203f, 20.232f, 49.09f)
        curveTo(20.833f, 48.977f, 21.511f, 48.853f, 22.268f, 48.719f)
        curveTo(23.024f, 48.585f, 23.795f, 48.401f, 24.579f, 48.168f)
        curveTo(25.364f, 47.934f, 26.131f, 47.637f, 26.881f, 47.277f)
        curveTo(27.63f, 46.916f, 28.309f, 46.453f, 28.917f, 45.888f)
        close()
        moveTo(21.281f, 58.889f)
        curveTo(21.352f, 59.058f, 21.437f, 59.242f, 21.536f, 59.44f)
        curveTo(21.621f, 59.624f, 21.734f, 59.836f, 21.875f, 60.076f)
        curveTo(22.017f, 60.317f, 22.186f, 60.578f, 22.384f, 60.861f)
        curveTo(22.625f, 61.2f, 22.869f, 61.557f, 23.116f, 61.932f)
        curveTo(23.363f, 62.307f, 23.551f, 62.646f, 23.678f, 62.95f)
        curveTo(23.805f, 63.254f, 23.844f, 63.505f, 23.795f, 63.703f)
        curveTo(23.745f, 63.901f, 23.544f, 64f, 23.19f, 64f)
        lineTo(21.981f, 64f)
        curveTo(21.699f, 64f, 21.437f, 63.947f, 21.197f, 63.841f)
        curveTo(20.956f, 63.735f, 20.726f, 63.572f, 20.507f, 63.353f)
        curveTo(20.288f, 63.134f, 20.062f, 62.855f, 19.829f, 62.515f)
        curveTo(19.595f, 62.176f, 19.344f, 61.773f, 19.076f, 61.306f)
        curveTo(18.751f, 60.769f, 18.524f, 60.299f, 18.397f, 59.896f)
        curveTo(18.27f, 59.493f, 18.185f, 59.157f, 18.143f, 58.889f)
        curveTo(18.086f, 58.578f, 18.086f, 58.309f, 18.143f, 58.083f)
        curveTo(18.256f, 58.097f, 18.39f, 58.125f, 18.546f, 58.168f)
        curveTo(18.673f, 58.196f, 18.828f, 58.231f, 19.012f, 58.274f)
        curveTo(19.196f, 58.316f, 19.408f, 58.358f, 19.648f, 58.401f)
        curveTo(19.889f, 58.457f, 20.104f, 58.51f, 20.295f, 58.56f)
        curveTo(20.486f, 58.609f, 20.659f, 58.662f, 20.815f, 58.719f)
        curveTo(20.985f, 58.775f, 21.14f, 58.832f, 21.281f, 58.889f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color.White,
            1f to Color(0xFFE1E2E4)
          ),
          start = Offset(61.303f, 28.112f),
          end = Offset(20.593f, 28.112f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(19.487f, 14f)
        lineTo(60.93f, 14f)
        curveTo(62.586f, 14f, 63.93f, 15.343f, 63.93f, 17f)
        lineTo(63.93f, 34.698f)
        curveTo(63.93f, 36.355f, 62.586f, 37.698f, 60.93f, 37.698f)
        lineTo(39.893f, 37.698f)
        lineTo(39.893f, 37.698f)
        lineTo(34.411f, 41.822f)
        curveTo(33.529f, 42.486f, 32.275f, 42.309f, 31.611f, 41.426f)
        curveTo(31.304f, 41.018f, 31.165f, 40.509f, 31.221f, 40.002f)
        lineTo(31.479f, 37.698f)
        lineTo(31.479f, 37.698f)
        lineTo(19.487f, 37.698f)
        curveTo(17.83f, 37.698f, 16.487f, 36.355f, 16.487f, 34.698f)
        lineTo(16.487f, 17f)
        curveTo(16.487f, 15.343f, 17.83f, 14f, 19.487f, 14f)
        close()
      }
      path(
        fill = SolidColor(Color(0xFF494954)),
        strokeLineWidth = 1f
      ) {
        moveTo(23.897f, 31.859f)
        lineTo(27.772f, 19.556f)
        lineTo(30.985f, 19.556f)
        lineTo(34.859f, 31.859f)
        lineTo(32.028f, 31.859f)
        lineTo(30.372f, 25.583f)
        curveTo(30.195f, 24.954f, 30.024f, 24.294f, 29.859f, 23.604f)
        curveTo(29.693f, 22.914f, 29.527f, 22.249f, 29.362f, 21.609f)
        lineTo(29.296f, 21.609f)
        curveTo(29.141f, 22.26f, 28.978f, 22.928f, 28.807f, 23.612f)
        curveTo(28.636f, 24.297f, 28.468f, 24.954f, 28.302f, 25.583f)
        lineTo(26.646f, 31.859f)
        lineTo(23.897f, 31.859f)
        close()
        moveTo(26.414f, 28.861f)
        lineTo(26.414f, 26.775f)
        lineTo(32.309f, 26.775f)
        lineTo(32.309f, 28.861f)
        lineTo(26.414f, 28.861f)
        close()
        moveTo(36.243f, 31.859f)
        lineTo(36.243f, 19.556f)
        lineTo(39.687f, 19.556f)
        curveTo(40.945f, 19.556f, 42.033f, 19.774f, 42.949f, 20.21f)
        curveTo(43.865f, 20.646f, 44.574f, 21.311f, 45.077f, 22.205f)
        curveTo(45.579f, 23.099f, 45.83f, 24.247f, 45.83f, 25.649f)
        curveTo(45.83f, 27.051f, 45.582f, 28.213f, 45.085f, 29.135f)
        curveTo(44.588f, 30.056f, 43.89f, 30.741f, 42.99f, 31.188f)
        curveTo(42.09f, 31.635f, 41.039f, 31.859f, 39.836f, 31.859f)
        lineTo(36.243f, 31.859f)
        close()
        moveTo(38.942f, 29.673f)
        lineTo(39.521f, 29.673f)
        curveTo(40.228f, 29.673f, 40.849f, 29.543f, 41.384f, 29.284f)
        curveTo(41.919f, 29.024f, 42.333f, 28.599f, 42.626f, 28.009f)
        curveTo(42.918f, 27.418f, 43.065f, 26.632f, 43.065f, 25.649f)
        curveTo(43.065f, 24.656f, 42.918f, 23.875f, 42.626f, 23.306f)
        curveTo(42.333f, 22.738f, 41.919f, 22.332f, 41.384f, 22.089f)
        curveTo(40.849f, 21.846f, 40.228f, 21.725f, 39.521f, 21.725f)
        lineTo(38.942f, 21.725f)
        lineTo(38.942f, 29.673f)
        close()
        moveTo(51.932f, 32.09f)
        curveTo(51.115f, 32.09f, 50.309f, 31.939f, 49.515f, 31.635f)
        curveTo(48.72f, 31.331f, 48.013f, 30.893f, 47.395f, 30.319f)
        lineTo(48.935f, 28.447f)
        curveTo(49.366f, 28.845f, 49.854f, 29.162f, 50.4f, 29.4f)
        curveTo(50.947f, 29.637f, 51.474f, 29.756f, 51.982f, 29.756f)
        curveTo(52.578f, 29.756f, 53.022f, 29.642f, 53.315f, 29.416f)
        curveTo(53.607f, 29.19f, 53.754f, 28.889f, 53.754f, 28.514f)
        curveTo(53.754f, 28.238f, 53.679f, 28.017f, 53.53f, 27.851f)
        curveTo(53.381f, 27.686f, 53.174f, 27.537f, 52.909f, 27.404f)
        curveTo(52.644f, 27.272f, 52.33f, 27.134f, 51.965f, 26.99f)
        lineTo(50.359f, 26.311f)
        curveTo(49.94f, 26.135f, 49.539f, 25.9f, 49.159f, 25.608f)
        curveTo(48.778f, 25.315f, 48.469f, 24.951f, 48.231f, 24.515f)
        curveTo(47.994f, 24.079f, 47.875f, 23.568f, 47.875f, 22.983f)
        curveTo(47.875f, 22.299f, 48.06f, 21.681f, 48.43f, 21.129f)
        curveTo(48.8f, 20.577f, 49.313f, 20.141f, 49.97f, 19.82f)
        curveTo(50.627f, 19.5f, 51.38f, 19.34f, 52.23f, 19.34f)
        curveTo(52.948f, 19.34f, 53.651f, 19.475f, 54.341f, 19.746f)
        curveTo(55.031f, 20.016f, 55.636f, 20.417f, 56.155f, 20.946f)
        lineTo(54.797f, 22.635f)
        curveTo(54.399f, 22.326f, 53.996f, 22.086f, 53.588f, 21.915f)
        curveTo(53.18f, 21.744f, 52.727f, 21.658f, 52.23f, 21.658f)
        curveTo(51.733f, 21.658f, 51.342f, 21.761f, 51.055f, 21.965f)
        curveTo(50.767f, 22.169f, 50.624f, 22.448f, 50.624f, 22.801f)
        curveTo(50.624f, 23.066f, 50.707f, 23.284f, 50.872f, 23.455f)
        curveTo(51.038f, 23.626f, 51.261f, 23.775f, 51.543f, 23.902f)
        curveTo(51.824f, 24.029f, 52.147f, 24.164f, 52.512f, 24.308f)
        lineTo(54.085f, 24.954f)
        curveTo(54.581f, 25.152f, 55.012f, 25.403f, 55.376f, 25.707f)
        curveTo(55.741f, 26.011f, 56.022f, 26.372f, 56.221f, 26.792f)
        curveTo(56.419f, 27.211f, 56.519f, 27.713f, 56.519f, 28.298f)
        curveTo(56.519f, 28.983f, 56.339f, 29.612f, 55.981f, 30.186f)
        curveTo(55.622f, 30.76f, 55.098f, 31.221f, 54.408f, 31.569f)
        curveTo(53.718f, 31.916f, 52.893f, 32.09f, 51.932f, 32.09f)
        close()
      }
    }.build()

    return _AdIcon!!
  }

@Suppress("ObjectPropertyName")
private var _AdIcon: ImageVector? = null

@Preview
@Composable
private fun AdIconPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.AdIcon, contentDescription = null)
  }
}
