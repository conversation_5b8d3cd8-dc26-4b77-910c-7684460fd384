package com.example.shorts.ui.node.screen.home.discover

import com.bytedance.sdk.shortplay.api.ShortPlay

data class CategoryDramaCache(
  val dramas: List<ShortPlay> = emptyList(),
  val currentPage: Int = 1,
  val hasMore: Boolean = true,
  val isLoading: Boolean = false
)

data class DiscoverUiState(
  val recommendedDramas: List<ShortPlay> = emptyList(),
  val newReleases: List<ShortPlay> = emptyList(),
  val categories: List<ShortPlay.ShortPlayCategory> = emptyList(),
  val selectedCategoryId: Long? = null,
  val categoryDramasCache: Map<Long, CategoryDramaCache> = emptyMap(),
  val isLoading: Boolean = true,
  val isLoadingCategories: Boolean = false,
  val error: String? = null
) {
  val categoryDramas: List<ShortPlay>
    get() = selectedCategoryId?.let { categoryDramasCache[it]?.dramas } ?: emptyList()

  val isLoadingCategoryDramas: Boolean
    get() = selectedCategoryId?.let { categoryDramasCache[it]?.isLoading } ?: false

  val hasMoreCategoryDramas: <PERSON>ole<PERSON>
    get() = selectedCategoryId?.let { categoryDramasCache[it]?.hasMore } ?: false
}
