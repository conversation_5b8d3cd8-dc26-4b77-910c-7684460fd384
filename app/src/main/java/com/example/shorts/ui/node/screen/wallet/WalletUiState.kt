package com.example.shorts.ui.node.screen.wallet

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import com.example.shorts.foundation.lyricist.AppStrings
import com.example.shorts.foundation.lyricist.globalAppStrings
import com.example.shorts.foundation.earn.Withdrawal
import com.example.shorts.foundation.number.formatWithCommas
import java.math.BigDecimal

/**
 * 钱包余额数据
 */
data class WalletBalance(
  val amount: BigDecimal,
  val currencySymbol: String = "$", // 货币符号，如 "$", "¥", "€" 等
  val currencyCode: String = "USD", // 货币代码，如 "USD", "CNY", "EUR" 等
) {
  val amountWithCommas get() = amount.formatWithCommas()

  val displayAmount get() = "${currencySymbol}${amountWithCommas}"
}

/**
 * 滚动弹幕消息
 */
data class ScrollingMessage(
  val id: String,
  val userName: String,
  val withdrawal: Withdrawal
) {
  fun getDisplayText(
    strings: AppStrings = globalAppStrings,
    highlightColor: Color = Color.White
  ): AnnotatedString {
    return strings.congratsWithdrawal(userName, withdrawal.displayAmount, highlightColor)
  }
}

/**
 * 钱包提现页面UI状态
 */
data class WalletUiState(
  val balance: WalletBalance = WalletBalance(
    amount = BigDecimal("0.00"),
    currencySymbol = "$",
    currencyCode = "USD"
  ),
  val withdrawals: List<Withdrawal> = emptyList(),
  val selectedWithdrawal: Withdrawal? = null,
  val isLoading: Boolean = false,
  val canWithdraw: Boolean = true,
)