package com.example.shorts.ui.node.screen.home.tasks

import com.example.shorts.foundation.earn.Task
import com.example.shorts.foundation.earn.TaskRepository
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.mvi_ui_model.UiModel
import org.koin.android.annotation.KoinViewModel

@KoinViewModel
class TasksUiModel(
  private val taskRepository: TaskRepository
) : UiModel<TasksUiState, Nothing>(TasksUiState()) {

  companion object {
    const val TAG = "TasksUiModel"
  }

  init {
    onLoad()
  }

  fun onLoad() = intent {
    debugLog(tag = TAG) { "onLoad" }
    loadTasks()
  }

  fun loadTasks() = intent {
    val tasks = taskRepository.getTasks()
    reduce { state.copy(tasks = tasks) }
  }

  fun onTaskClick(task: Task) = intent {
    if (task.canClaimReward) {
      // Claim reward
      debugLog(tag = TAG) { "onTaskClick: canClaimReward" }
      claimReward(task.template.id)
    } else if (task.isCompleted) {
      // Already claimed, do nothing or show message
      debugLog(tag = TAG) { "onTaskClick: isCompleted" }
    } else {
      // Navigate to task execution (watch ads/drama)
      debugLog(tag = TAG) { "onTaskClick: isNotCompleted" }
    }
  }

  fun claimReward(taskId: String) = intent {
    taskRepository.claimReward(taskId)
    loadTasks() // Refresh tasks after claiming reward
  }

}