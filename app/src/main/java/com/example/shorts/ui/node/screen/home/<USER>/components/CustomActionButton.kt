package com.example.shorts.ui.node.screen.home.shorts.components

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatImageView
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.bytedance.sdk.shortplay.api.ShortPlayFragment

class CustomActionButton @JvmOverloads constructor(
  context: Context,
  attrs: AttributeSet? = null,
  defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr), PSSDK.IControlView {

  init {
    setupButton()
  }

  private fun setupButton() {
    // 创建圆形背景
    val background = GradientDrawable().apply {
      shape = GradientDrawable.OVAL
      setColor(Color.parseColor("#80000000")) // 半透明黑色
    }
    
    setBackground(background)
    scaleType = ScaleType.CENTER_INSIDE
    setPadding(12, 12, 12, 12)
    
    // 设置点击效果
    isClickable = true
    isFocusable = true
    
    // 添加点击动画效果
    setOnTouchListener { v, event ->
      when (event.action) {
        android.view.MotionEvent.ACTION_DOWN -> {
          animate().scaleX(0.9f).scaleY(0.9f).setDuration(100).start()
        }
        android.view.MotionEvent.ACTION_UP, android.view.MotionEvent.ACTION_CANCEL -> {
          animate().scaleX(1.0f).scaleY(1.0f).setDuration(100).start()
        }
      }
      false
    }
  }

  // 实现PSSDK.IControlView接口
  override fun getControlViewType(): PSSDK.ControlViewType {
    return PSSDK.ControlViewType.CUSTOM
  }

  override fun bindItemData(fragment: ShortPlayFragment?, shortPlay: ShortPlay?, index: Int) {
    // 不需要特殊处理，按钮的逻辑在onClick中处理
  }
}
