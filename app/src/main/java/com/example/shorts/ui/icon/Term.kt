package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.Term: ImageVector
  get() {
    if (_Term != null) {
      return _Term!!
    }
    _Term = ImageVector.Builder(
      name = "Term",
      defaultWidth = 48.dp,
      defaultHeight = 48.dp,
      viewportWidth = 48f,
      viewportHeight = 48f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(15.171f, 6.834f)
        lineTo(32.829f, 6.834f)
        arcTo(6f, 6f, 0f, isMoreThanHalf = false, isPositiveArc = true, 38.829f, 12.834f)
        lineTo(38.829f, 35.166f)
        arcTo(6f, 6f, 0f, isMoreThanHalf = false, isPositiveArc = true, 32.829f, 41.166f)
        lineTo(15.171f, 41.166f)
        arcTo(6f, 6f, 0f, isMoreThanHalf = false, isPositiveArc = true, 9.171f, 35.166f)
        lineTo(9.171f, 12.834f)
        arcTo(6f, 6f, 0f, isMoreThanHalf = false, isPositiveArc = true, 15.171f, 6.834f)
        close()
      }
      path(
        fill = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(14.213f, 12.3f)
        lineTo(14.213f, 19.571f)
        curveTo(14.213f, 20.123f, 14.661f, 20.571f, 15.213f, 20.571f)
        curveTo(15.509f, 20.571f, 15.79f, 20.44f, 15.98f, 20.213f)
        lineTo(18.134f, 17.642f)
        lineTo(18.134f, 17.642f)
        lineTo(20.287f, 20.213f)
        curveTo(20.642f, 20.636f, 21.273f, 20.692f, 21.696f, 20.337f)
        curveTo(21.923f, 20.147f, 22.054f, 19.867f, 22.054f, 19.571f)
        lineTo(22.054f, 12.3f)
        curveTo(22.054f, 11.748f, 21.606f, 11.3f, 21.054f, 11.3f)
        lineTo(15.213f, 11.3f)
        curveTo(14.661f, 11.3f, 14.213f, 11.748f, 14.213f, 12.3f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(15.607f, 26.829f)
        lineTo(27.936f, 26.829f)
      }
      path(
        stroke = SolidColor(Color(0xFFD064FE)),
        strokeLineWidth = 3f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(15.607f, 34.312f)
        lineTo(26.996f, 34.312f)
      }
    }.build()

    return _Term!!
  }

@Suppress("ObjectPropertyName")
private var _Term: ImageVector? = null

@Preview
@Composable
private fun TermPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.Term, contentDescription = null)
  }
}
