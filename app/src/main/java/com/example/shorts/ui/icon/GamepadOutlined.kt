package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.GamepadOutlined: ImageVector
  get() {
    if (_GamepadOutlined != null) {
      return _GamepadOutlined!!
    }
    _GamepadOutlined = ImageVector.Builder(
      name = "GamepadOutlined",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(47.294f, 15f)
        curveTo(49.321f, 15f, 51.045f, 15.489f, 52.514f, 16.418f)
        curveTo(54.101f, 17.421f, 55.387f, 18.928f, 56.459f, 20.837f)
        curveTo(59.108f, 25.55f, 60.447f, 32.638f, 61.409f, 41.004f)
        curveTo(61.843f, 45.358f, 61.871f, 49.321f, 61.008f, 52.009f)
        curveTo(60.687f, 53.01f, 60.246f, 53.824f, 59.611f, 54.354f)
        curveTo(59.076f, 54.802f, 58.402f, 55.021f, 57.579f, 54.998f)
        curveTo(53.868f, 54.998f, 52.216f, 53.065f, 50.63f, 50.939f)
        lineTo(50.342f, 50.551f)
        lineTo(50.198f, 50.356f)
        curveTo(48.907f, 48.614f, 47.555f, 46.827f, 45.444f, 45.571f)
        curveTo(45.256f, 45.461f, 45.065f, 45.343f, 44.866f, 45.22f)
        curveTo(43.087f, 44.121f, 40.736f, 42.653f, 35f, 42.653f)
        curveTo(29.687f, 42.653f, 27.368f, 44.044f, 25.694f, 45.039f)
        curveTo(25.38f, 45.226f, 25.092f, 45.398f, 24.809f, 45.539f)
        curveTo(22.638f, 46.827f, 21.286f, 48.614f, 19.997f, 50.354f)
        lineTo(19.514f, 51.003f)
        curveTo(17.935f, 53.099f, 16.291f, 54.998f, 12.642f, 54.998f)
        curveTo(11.766f, 54.998f, 11.047f, 54.747f, 10.474f, 54.265f)
        curveTo(9.807f, 53.704f, 9.339f, 52.861f, 9f, 51.826f)
        curveTo(8.115f, 49.121f, 8.142f, 45.186f, 8.647f, 40.858f)
        curveTo(9.534f, 32.575f, 10.837f, 25.529f, 13.465f, 20.833f)
        curveTo(14.531f, 18.928f, 15.814f, 17.422f, 17.401f, 16.418f)
        curveTo(18.869f, 15.49f, 20.593f, 15f, 22.622f, 15f)
        curveTo(25.094f, 15f, 26.872f, 16.393f, 28.644f, 17.706f)
        curveTo(30.551f, 19.119f, 32.452f, 20.466f, 34.87f, 20.679f)
        curveTo(37.375f, 20.467f, 39.261f, 19.144f, 41.166f, 17.744f)
        curveTo(42.971f, 16.418f, 44.797f, 15f, 47.294f, 15f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(23.679f, 25.476f)
        curveTo(25.42f, 25.476f, 26.98f, 26.17f, 28.105f, 27.295f)
        curveTo(29.23f, 28.42f, 29.924f, 29.98f, 29.924f, 31.722f)
        curveTo(29.924f, 33.448f, 29.217f, 34.999f, 28.092f, 36.123f)
        curveTo(26.949f, 37.267f, 25.374f, 37.967f, 23.679f, 37.967f)
        curveTo(21.952f, 37.967f, 20.401f, 37.26f, 19.277f, 36.135f)
        curveTo(18.133f, 34.992f, 17.434f, 33.417f, 17.434f, 31.722f)
        curveTo(17.434f, 30.026f, 18.133f, 28.451f, 19.277f, 27.308f)
        curveTo(20.401f, 26.183f, 21.952f, 25.476f, 23.679f, 25.476f)
        close()
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(40.632f, 31.722f)
        lineTo(53.932f, 31.722f)
      }
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(47.282f, 25.071f)
        lineTo(47.282f, 38.372f)
      }
    }.build()

    return _GamepadOutlined!!
  }

@Suppress("ObjectPropertyName")
private var _GamepadOutlined: ImageVector? = null

@Preview
@Composable
private fun GamepadOutlinedPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.GamepadOutlined, contentDescription = null)
  }
}
