package com.example.shorts.ui.node.screen.home.shorts.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import com.bytedance.sdk.shortplay.api.ShortPlay

@Composable
fun VideoFeedItem(
  shortPlay: ShortPlay,
  isPlaying: Boolean = false,
  onLikeClick: (ShortPlay) -> Unit = {},
  onShareClick: (ShortPlay) -> Unit = {},
  modifier: Modifier = Modifier
) {
  Box(
    modifier = modifier.fillMaxSize()
  ) {
    if (isPlaying) {
      // 当前页面正在播放，显示视频播放器
      InlineVideoPlayer(
        shortPlay = shortPlay,
        isPlaying = isPlaying,
        modifier = Modifier.fillMaxSize()
      )
    } else {
      // 非当前页面，显示封面图片
      AsyncImage(
        model = ImageRequest.Builder(LocalContext.current)
          .data(shortPlay.coverImage)
          .build(),
        contentDescription = shortPlay.title,
        contentScale = ContentScale.Crop,
        modifier = Modifier.fillMaxSize()
      )

      // 渐变遮罩
      Box(
        modifier = Modifier
          .fillMaxSize()
          .background(
            Brush.verticalGradient(
              colors = listOf(
                Color.Transparent,
                Color.Black.copy(alpha = 0.3f),
                Color.Black.copy(alpha = 0.7f)
              ),
              startY = 0f,
              endY = Float.POSITIVE_INFINITY
            )
          )
      )

      // 播放按钮（仅在非播放状态显示）
      Box(
        modifier = Modifier
          .align(Alignment.Center)
          .size(80.dp)
          .background(
            Color.Black.copy(alpha = 0.5f),
            CircleShape
          ),
        contentAlignment = Alignment.Center
      ) {
        Icon(
          imageVector = Icons.Default.PlayArrow,
          contentDescription = "Play",
          tint = Color.White,
          modifier = Modifier.size(40.dp)
        )
      }
    }

    // 右侧操作按钮
    Column(
      modifier = Modifier
        .align(Alignment.CenterEnd)
        .padding(16.dp),
      verticalArrangement = Arrangement.spacedBy(16.dp),
      horizontalAlignment = Alignment.CenterHorizontally
    ) {
      // 点赞按钮
      ActionButton(
        icon = Icons.Default.Favorite,
        count = shortPlay.totalCollectCount,
        isSelected = shortPlay.isCollected,
        onClick = { onLikeClick(shortPlay) }
      )

      // 分享按钮
      ActionButton(
        icon = Icons.Default.Share,
        onClick = { onShareClick(shortPlay) }
      )
    }

    // 底部信息
    Column(
      modifier = Modifier
        .align(Alignment.BottomStart)
        .padding(16.dp)
        .fillMaxWidth(0.7f)
    ) {
      Text(
        text = shortPlay.title,
        color = Color.White,
        fontSize = 16.sp,
        fontWeight = FontWeight.Bold,
        maxLines = 2,
        overflow = TextOverflow.Ellipsis
      )

      Spacer(modifier = Modifier.height(8.dp))

      Text(
        text = shortPlay.desc,
        color = Color.White.copy(alpha = 0.8f),
        fontSize = 14.sp,
        maxLines = 3,
        overflow = TextOverflow.Ellipsis
      )

      Spacer(modifier = Modifier.height(8.dp))

      // 剧集信息
      Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
      ) {
        AssistChip(
          onClick = { },
          label = {
            Text(
              text = "${shortPlay.total}集",
              fontSize = 12.sp
            )
          },
          colors = AssistChipDefaults.assistChipColors(
            containerColor = Color.White.copy(alpha = 0.2f),
            labelColor = Color.White
          ),
          border = null
        )

        if (shortPlay.progressState == ShortPlay.PROGRESS_STATE_END) {
          AssistChip(
            onClick = { },
            label = {
              Text(
                text = "完结",
                fontSize = 12.sp
              )
            },
            colors = AssistChipDefaults.assistChipColors(
              containerColor = Color.Green.copy(alpha = 0.8f),
              labelColor = Color.White
            ),
            border = null
          )
        }
      }
    }
  }
}

@Composable
private fun ActionButton(
  icon: androidx.compose.ui.graphics.vector.ImageVector,
  count: Int? = null,
  isSelected: Boolean = false,
  onClick: () -> Unit
) {
  Column(
    horizontalAlignment = Alignment.CenterHorizontally,
    verticalArrangement = Arrangement.spacedBy(4.dp)
  ) {
    Box(
      modifier = Modifier
        .size(48.dp)
        .background(
          Color.Black.copy(alpha = 0.5f),
          CircleShape
        )
        .clickable { onClick() },
      contentAlignment = Alignment.Center
    ) {
      Icon(
        imageVector = icon,
        contentDescription = null,
        tint = if (isSelected) Color.Red else Color.White,
        modifier = Modifier.size(24.dp)
      )
    }

    count?.let {
      if (it > 0) {
        Text(
          text = formatCount(it),
          color = Color.White,
          fontSize = 12.sp
        )
      }
    }
  }
}

private fun formatCount(count: Int): String {
  return when {
    count >= 1000000 -> "${count / 1000000}M"
    count >= 1000 -> "${count / 1000}K"
    else -> count.toString()
  }
}
