package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.GamepadFilled: ImageVector
  get() {
    if (_GamepadFilled != null) {
      return _GamepadFilled!!
    }
    _GamepadFilled = ImageVector.Builder(
      name = "GamepadFilled",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFD469FF),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(35f, 22.931f),
          end = Offset(25.515f, 46.317f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(47.294f, 14f)
        curveTo(57.412f, 14f, 60.601f, 25.227f, 62.403f, 40.89f)
        curveTo(63.234f, 49.206f, 62.541f, 56.137f, 57.551f, 55.998f)
        curveTo(50.344f, 55.998f, 50.066f, 49.484f, 44.938f, 46.434f)
        curveTo(43.971f, 45.951f, 41.089f, 43.653f, 35f, 43.653f)
        curveTo(28.911f, 43.653f, 26.229f, 45.947f, 25.256f, 46.434f)
        curveTo(23.186f, 47.665f, 21.907f, 49.459f, 20.643f, 51.162f)
        lineTo(20.313f, 51.604f)
        curveTo(18.551f, 53.945f, 16.689f, 55.998f, 12.642f, 55.998f)
        curveTo(7.514f, 55.998f, 6.682f, 49.068f, 7.653f, 40.751f)
        curveTo(9.316f, 25.227f, 12.504f, 14f, 22.622f, 14f)
        curveTo(27.751f, 14f, 30.246f, 19.267f, 34.958f, 19.683f)
        curveTo(39.532f, 19.267f, 42.166f, 14f, 47.294f, 14f)
        close()
        moveTo(23.679f, 25.071f)
        curveTo(19.97f, 25.071f, 17.029f, 28.141f, 17.029f, 31.722f)
        curveTo(17.029f, 35.302f, 19.97f, 38.372f, 23.679f, 38.372f)
        curveTo(27.259f, 38.372f, 30.329f, 35.43f, 30.329f, 31.722f)
        curveTo(30.329f, 28.013f, 27.387f, 25.071f, 23.679f, 25.071f)
        close()
        moveTo(48.282f, 25.071f)
        lineTo(46.282f, 25.071f)
        lineTo(46.282f, 30.721f)
        lineTo(40.632f, 30.722f)
        lineTo(40.632f, 32.722f)
        lineTo(46.282f, 32.721f)
        lineTo(46.282f, 38.372f)
        lineTo(48.282f, 38.372f)
        lineTo(48.282f, 32.721f)
        lineTo(53.932f, 32.722f)
        lineTo(53.932f, 30.722f)
        lineTo(48.282f, 30.721f)
        lineTo(48.282f, 25.071f)
        close()
      }
    }.build()

    return _GamepadFilled!!
  }

@Suppress("ObjectPropertyName")
private var _GamepadFilled: ImageVector? = null

@Preview
@Composable
private fun GamepadFilledPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.GamepadFilled, contentDescription = null)
  }
}
