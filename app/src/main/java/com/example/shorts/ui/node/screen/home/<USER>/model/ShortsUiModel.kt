package com.example.shorts.ui.node.screen.home.shorts.model

import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.mvi_ui_model.UiModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.koin.android.annotation.KoinViewModel
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

@KoinViewModel
class ShortsUiModel : UiModel<ShortsUiState, Nothing>(ShortsUiState()) {

  companion object {
    private const val TAG = "ShortsUiModel"
    private const val PAGE_SIZE = 20
  }

  init {
    loadShortPlays()
  }

  fun loadShortPlays() = intent {
    debugLog(tag = TAG) { "loadShortPlays: isLoading=${state.isLoading}, hasMore=${state.hasMore}, currentPage=${state.currentPage}" }
    if (state.isLoading) return@intent
    
    reduce { state.copy(isLoading = true, error = null) }
    
    try {
      val result = requestFeedList(state.currentPage, PAGE_SIZE)
      reduce {
        state.copy(
          shortPlays = if (state.currentPage == 1) result.dataList else state.shortPlays + result.dataList,
          hasMore = result.hasMore,
          currentPage = state.currentPage + 1,
          isLoading = false,
          error = null
        )
      }
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Failed to load short plays: ${e.message}" }
      reduce {
        state.copy(
          isLoading = false,
          error = e.message ?: "Unknown error"
        )
      }
    }
  }

  fun refresh() = intent {
    if (state.isRefreshing) return@intent
    
    reduce { state.copy(isRefreshing = true, currentPage = 1, error = null) }
    
    try {
      val result = requestFeedList(1, PAGE_SIZE)
      reduce {
        state.copy(
          shortPlays = result.dataList,
          hasMore = result.hasMore,
          currentPage = 2,
          isRefreshing = false,
          error = null
        )
      }
    } catch (e: Exception) {
      debugLog(tag = TAG) { "Failed to refresh short plays: ${e.message}" }
      reduce {
        state.copy(
          isRefreshing = false,
          error = e.message ?: "Unknown error"
        )
      }
    }
  }

  fun loadMore() = intent {
    if (state.isLoading || !state.hasMore) return@intent
    loadShortPlays()
  }

  private suspend fun requestFeedList(page: Int, count: Int): PSSDK.FeedListLoadResult<ShortPlay> {
    return withContext(Dispatchers.IO) {
      suspendCoroutine { continuation ->
        PSSDK.requestFeedList(page, count, object : PSSDK.FeedListResultListener {
          override fun onSuccess(result: PSSDK.FeedListLoadResult<ShortPlay>) {
            continuation.resume(result)
          }

          override fun onFail(errorInfo: PSSDK.ErrorInfo) {
            continuation.resumeWith(
              Result.failure(
                Exception("Error ${errorInfo.code}: ${errorInfo.msg}")
              )
            )
          }
        })
      }
    }
  }
}
