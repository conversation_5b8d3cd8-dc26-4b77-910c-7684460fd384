package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.AdVideo: ImageVector
  get() {
    if (_AdVideo != null) {
      return _AdVideo!!
    }
    _AdVideo = ImageVector.Builder(
      name = "AdVideo",
      defaultWidth = 178.dp,
      defaultHeight = 178.dp,
      viewportWidth = 178f,
      viewportHeight = 178f
    ).apply {
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFAB5CFE),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(112.85f, 44.83f),
          end = Offset(50.52f, 89f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(123.02f, 15.44f)
        curveTo(132.97f, 15.44f, 141.02f, 23.5f, 141.02f, 33.44f)
        lineTo(141.02f, 101.55f)
        lineTo(111.18f, 101.55f)
        curveTo(103f, 101.55f, 96.35f, 108.1f, 96.18f, 116.24f)
        lineTo(96.18f, 116.55f)
        lineTo(96.18f, 158.31f)
        curveTo(96.18f, 159.78f, 96.39f, 161.2f, 96.78f, 162.55f)
        lineTo(37.93f, 162.55f)
        curveTo(27.99f, 162.55f, 19.93f, 154.49f, 19.93f, 144.55f)
        lineTo(19.93f, 33.44f)
        curveTo(19.93f, 23.5f, 27.99f, 15.44f, 37.93f, 15.44f)
        lineTo(123.02f, 15.44f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFAB5CFE),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(152.21f, 118.94f),
          end = Offset(109.95f, 135.64f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(114.45f, 107.83f)
        lineTo(146.07f, 107.83f)
        arcTo(12f, 12f, 0f, isMoreThanHalf = false, isPositiveArc = true, 158.07f, 119.83f)
        lineTo(158.07f, 151.45f)
        arcTo(12f, 12f, 0f, isMoreThanHalf = false, isPositiveArc = true, 146.07f, 163.45f)
        lineTo(114.45f, 163.45f)
        arcTo(12f, 12f, 0f, isMoreThanHalf = false, isPositiveArc = true, 102.45f, 151.45f)
        lineTo(102.45f, 119.83f)
        arcTo(12f, 12f, 0f, isMoreThanHalf = false, isPositiveArc = true, 114.45f, 107.83f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(141.77f, 138.58f)
        lineTo(124.74f, 149.35f)
        curveTo(122.87f, 150.53f, 120.4f, 149.97f, 119.22f, 148.1f)
        curveTo(118.81f, 147.46f, 118.6f, 146.72f, 118.6f, 145.97f)
        lineTo(118.6f, 125.48f)
        curveTo(118.6f, 123.27f, 120.39f, 121.48f, 122.6f, 121.48f)
        curveTo(123.3f, 121.48f, 123.98f, 121.66f, 124.58f, 122.01f)
        lineTo(141.62f, 131.72f)
        curveTo(143.53f, 132.82f, 144.2f, 135.26f, 143.11f, 137.18f)
        curveTo(142.78f, 137.75f, 142.32f, 138.23f, 141.77f, 138.58f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(63.98f, 32.49f)
        lineTo(80.93f, 77.34f)
        lineTo(73.16f, 77.34f)
        lineTo(69.13f, 66.09f)
        lineTo(50.57f, 66.09f)
        lineTo(46.54f, 77.34f)
        lineTo(38.77f, 77.34f)
        lineTo(55.73f, 32.49f)
        lineTo(63.98f, 32.49f)
        close()
        moveTo(101.53f, 32.49f)
        curveTo(108.65f, 32.49f, 114.07f, 34.49f, 117.77f, 38.53f)
        curveTo(121.31f, 42.33f, 123.08f, 47.79f, 123.08f, 54.92f)
        curveTo(123.08f, 62f, 121.31f, 67.46f, 117.77f, 71.31f)
        curveTo(114.07f, 75.33f, 108.65f, 77.34f, 101.53f, 77.34f)
        lineTo(85.41f, 77.34f)
        lineTo(85.41f, 32.49f)
        close()
        moveTo(99.24f, 38.77f)
        lineTo(91.69f, 38.77f)
        lineTo(91.69f, 71.06f)
        lineTo(99.24f, 71.06f)
        curveTo(104.73f, 71.06f, 108.74f, 69.76f, 111.3f, 67.16f)
        curveTo(113.77f, 64.53f, 115.01f, 60.43f, 115.01f, 54.92f)
        curveTo(115.01f, 49.22f, 113.77f, 45.11f, 111.3f, 42.6f)
        curveTo(108.79f, 40.05f, 104.77f, 38.77f, 99.24f, 38.77f)
        close()
        moveTo(59.55f, 40.56f)
        lineTo(59.3f, 40.56f)
        lineTo(52.22f, 60.29f)
        lineTo(66.58f, 60.29f)
        lineTo(59.55f, 40.56f)
        close()
      }
      path(
        fill = SolidColor(Color.White),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(84.34f, 134.74f)
        lineTo(36.26f, 134.74f)
        curveTo(34.67f, 134.74f, 33.39f, 136.15f, 33.39f, 137.88f)
        curveTo(33.39f, 139.61f, 34.67f, 141.02f, 36.26f, 141.02f)
        lineTo(84.34f, 141.02f)
        curveTo(85.92f, 141.02f, 87.21f, 139.61f, 87.21f, 137.88f)
        curveTo(87.21f, 136.15f, 85.92f, 134.74f, 84.34f, 134.74f)
        close()
        moveTo(84.36f, 111.42f)
        lineTo(36.23f, 111.42f)
        curveTo(34.66f, 111.42f, 33.39f, 112.83f, 33.39f, 114.56f)
        curveTo(33.39f, 116.29f, 34.66f, 117.7f, 36.23f, 117.7f)
        lineTo(84.36f, 117.7f)
        curveTo(85.93f, 117.7f, 87.21f, 116.29f, 87.21f, 114.56f)
        curveTo(87.21f, 112.83f, 85.93f, 111.42f, 84.36f, 111.42f)
        close()
      }
    }.build()

    return _AdVideo!!
  }

@Suppress("ObjectPropertyName")
private var _AdVideo: ImageVector? = null

@Preview
@Composable
private fun AdVideoPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.AdVideo, contentDescription = null)
  }
}
