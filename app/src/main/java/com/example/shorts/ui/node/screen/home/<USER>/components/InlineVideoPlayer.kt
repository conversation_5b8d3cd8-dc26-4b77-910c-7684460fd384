package com.example.shorts.ui.node.screen.home.shorts.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentContainerView
import com.bytedance.sdk.shortplay.api.PSSDK
import com.bytedance.sdk.shortplay.api.ShortPlay
import com.bytedance.sdk.shortplay.api.ShortPlayFragment
import com.example.shorts.foundation.kermit.debugLog

@Composable
fun InlineVideoPlayer(
  shortPlay: ShortPlay,
  isPlaying: Boolean,
  modifier: Modifier = Modifier
) {
  val context = LocalContext.current
  val activity = context as? FragmentActivity
  
  var detailFragment by remember(shortPlay.id) { mutableStateOf<ShortPlayFragment?>(null) }
  var isLoading by remember(shortPlay.id) { mutableStateOf(true) }
  var error by remember(shortPlay.id) { mutableStateOf<String?>(null) }

  LaunchedEffect(shortPlay.id) {
    if (activity != null) {
      try {
        val builder = PSSDK.DetailPageConfig.Builder()
        builder
          .hideLeftTopCloseAndTitle(true, null) // 隐藏顶部标题栏
          .displayBottomExtraView(false) // 不显示底部额外视图
          .displayProgressBar(false) // 隐藏进度条，因为是Feed模式
          .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_TITLE, false) // 隐藏底部标题
          .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_DESC, false) // 隐藏底部描述
          .playSingleItem(true) // 只播放单个视频，不允许滑动切换
          .enableImmersiveMode(0) // 禁用沉浸式模式

        val fragment = PSSDK.createDetailFragment(shortPlay, builder.build(), object : PSSDK.ShortPlayDetailPageListener {
          override fun onShortPlayPlayed(shortPlay: ShortPlay, index: Int) {
            debugLog(tag = "InlineVideoPlayer") { "Playing episode $index of ${shortPlay.title}" }
          }

          override fun onOverScroll(direction: Int) {
            // 在Feed模式下不处理过度滚动
          }

          override fun onProgressChange(shortPlay: ShortPlay, index: Int, currentPlayTime: Int, duration: Int) {
            // 可以在这里处理播放进度
          }

          override fun onPlayFailed(errorInfo: PSSDK.ErrorInfo): Boolean {
            debugLog(tag = "InlineVideoPlayer") { "Play failed: ${errorInfo.code} - ${errorInfo.msg}" }
            error = "播放失败: ${errorInfo.msg}"
            return false
          }

          override fun onItemSelected(position: Int, type: PSSDK.ShortPlayDetailPageListener.ItemType, index: Int) {
            // Feed模式下不需要处理
          }

          override fun onVideoPlayStateChanged(shortPlay: ShortPlay, index: Int, playbackState: Int) {
            debugLog(tag = "InlineVideoPlayer") { "Play state changed: $playbackState" }
          }

          override fun onVideoPlayCompleted(shortPlay: ShortPlay, index: Int) {
            debugLog(tag = "InlineVideoPlayer") { "Video completed: episode $index" }
          }

          override fun onEnterImmersiveMode() {
            // Feed模式下不使用沉浸式模式
          }

          override fun onExitImmersiveMode() {
            // Feed模式下不使用沉浸式模式
          }

          override fun isNeedBlock(shortPlay: ShortPlay, index: Int): Boolean {
            // Feed模式下暂时不实现解锁逻辑
            return false
          }

          override fun showAdIfNeed(shortPlay: ShortPlay, index: Int, listener: PSSDK.ShortPlayBlockResultListener) {
            listener.onShortPlayUnlocked()
          }

          override fun onVideoInfoFetched(shortPlay: ShortPlay, index: Int, videoPlayInfo: PSSDK.VideoPlayInfo) {
            debugLog(tag = "InlineVideoPlayer") { "Video info fetched for episode $index" }
          }

          override fun onObtainPlayerControlViews(): List<android.view.View>? {
            // Feed模式下不添加额外的控制视图
            return null
          }
        })

        detailFragment = fragment
        isLoading = false
      } catch (e: Exception) {
        debugLog(tag = "InlineVideoPlayer") { "Failed to create detail fragment: ${e.message}" }
        error = "创建播放器失败: ${e.message}"
        isLoading = false
      }
    } else {
      error = "无法获取Activity实例"
      isLoading = false
    }
  }

  // 控制播放状态
  LaunchedEffect(isPlaying, detailFragment) {
    detailFragment?.let { fragment ->
      if (isPlaying) {
        fragment.startPlay()
      } else {
        fragment.pausePlay()
      }
    }
  }

  Box(
    modifier = modifier
      .fillMaxSize()
      .background(Color.Black)
  ) {
    when {
      isLoading -> {
        CircularProgressIndicator(
          modifier = Modifier.align(Alignment.Center),
          color = Color.White
        )
      }
      
      error != null -> {
        Text(
          text = error!!,
          color = Color.White,
          style = MaterialTheme.typography.bodyLarge,
          modifier = Modifier.align(Alignment.Center)
        )
      }
      
      detailFragment != null && activity != null -> {
        AndroidView(
          factory = { context ->
            FragmentContainerView(context).apply {
              id = android.view.View.generateViewId()
            }
          },
          modifier = Modifier.fillMaxSize()
        ) { fragmentContainer ->
          val fragmentManager = activity.supportFragmentManager
          val existingFragment = fragmentManager.findFragmentById(fragmentContainer.id)
          
          if (existingFragment != detailFragment) {
            val transaction = fragmentManager.beginTransaction()
            if (existingFragment != null) {
              transaction.remove(existingFragment)
            }
            transaction.replace(fragmentContainer.id, detailFragment!!)
            transaction.commitNow()
          }
        }
      }
    }
  }

  DisposableEffect(detailFragment) {
    onDispose {
      if (activity != null && detailFragment != null) {
        try {
          val fragmentManager = activity.supportFragmentManager
          val transaction = fragmentManager.beginTransaction()
          transaction.remove(detailFragment!!)
          transaction.commitNow()
        } catch (e: Exception) {
          debugLog(tag = "InlineVideoPlayer") { "Failed to remove fragment: ${e.message}" }
        }
      }
    }
  }
}
