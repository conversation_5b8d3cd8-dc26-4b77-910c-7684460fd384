package com.example.shorts.ui.theme

import android.app.Activity
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
  // 主要颜色 - 基于紫色图标和橙黄色按钮
  primary = Color(0xFF9C4FFF),           // 主紫色 - 历史记录图标颜色
  onPrimary = Color.White,         // 主色上的文本
  primaryContainer = Color(0xFF7B2FE6),  // 主色容器 - 更深的紫色
  onPrimaryContainer = Color(0xFFE8D5FF), // 主色容器上的文本

  // 次要颜色 - 橙黄色调（基于 Withdraw 按钮）
  secondary = Color(0xFFFFB547),         // 橙黄色
  onSecondary = Color(0xFF2A1800),       // 次要色上的文本
  secondaryContainer = Color(0xFF3F2700), // 次要色容器
  onSecondaryContainer = Color(0xFFFFDCBE), // 次要色容器上的文本

  // 第三色 - 深棕色调（基于卡片背景）
  tertiary = Color(0xFF8B7355),          // 深棕色
  onTertiary = Color(0xFF2A2018),        // 第三色上的文本
  tertiaryContainer = Color(0xFF42362C), // 第三色容器
  onTertiaryContainer = Color(0xFFE8D5C4), // 第三色容器上的文本

  // 背景色 - 基于图片的深色背景
  background = Color(0xFF1C1B1F),        // 主背景 - 深灰蓝色
  onBackground = Color(0xFFE6E1E5),      // 背景上的文本
  surface = Color(0xFF2B2831),           // 表面色 - 卡片背景色
  onSurface = Color(0xFFE6E1E5),         // 表面上的文本

  // 表面变体
  surfaceVariant = Color(0xFF49454F),    // 表面变体 - 分割线颜色
  onSurfaceVariant = Color(0xFFCAC4D0),  // 表面变体上的文本
  surfaceTint = Color(0xFF9C4FFF),       // 表面色调

  // 边框和分割线
  outline = Color(0xFF938F96),           // 轮廓线
  outlineVariant = Color(0xFF49454F),    // 轮廓线变体

  // 反色表面
  inverseSurface = Color(0xFFE6E1E5),    // 反色表面
  inverseOnSurface = Color(0xFF322F35),   // 反色表面上的文本
  inversePrimary = Color(0xFF6750A4),     // 反色主色

  // 错误色
  error = Color(0xFFFFB4AB),             // 错误色
  onError = Color(0xFF690005),           // 错误色上的文本
  errorContainer = Color(0xFF93000A),     // 错误容器
  onErrorContainer = Color(0xFFFFDAD6),   // 错误容器上的文本

  // 表面容器色调
  surfaceContainer = Color(0xFF211F26),          // 表面容器
  surfaceContainerHigh = Color(0xFF2B2831),      // 高级表面容器
  surfaceContainerHighest = Color(0xFF36333B),   // 最高级表面容器
  surfaceContainerLow = Color(0xFF1D1B20),       // 低级表面容器
  surfaceContainerLowest = Color(0xFF0F0D13),    // 最低级表面容器

  // 表面明亮和暗淡色
  surfaceBright = Color(0xFF3B383E),      // 明亮表面
  surfaceDim = Color(0xFF141218)          // 暗淡表面
)

@Composable
fun AppTheme(
  content: @Composable () -> Unit
) {
  val view = LocalView.current
  if (!view.isInEditMode) {
    SideEffect {
      val window = (view.context as Activity).window
      WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = false
    }
  }

  CompositionLocalProvider(
    LocalContentColor provides Color.White
  ) {
    MaterialTheme(
      colorScheme = DarkColorScheme,
      content = content
    )
  }
}