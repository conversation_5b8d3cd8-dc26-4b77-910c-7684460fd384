package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.HomeOutlined: ImageVector
  get() {
    if (_HomeOutlined != null) {
      return _HomeOutlined!!
    }
    _HomeOutlined = ImageVector.Builder(
      name = "HomeOutlined",
      defaultWidth = 70.dp,
      defaultHeight = 70.dp,
      viewportWidth = 70f,
      viewportHeight = 70f
    ).apply {
      path(
        stroke = SolidColor(Color(0xFFA1A1A1)),
        strokeLineWidth = 2f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(35f, 11.31f)
        curveTo(35.948f, 11.31f, 36.895f, 11.645f, 37.652f, 12.315f)
        lineTo(55.648f, 28.258f)
        curveTo(56.291f, 28.827f, 56.658f, 29.645f, 56.658f, 30.503f)
        lineTo(56.658f, 52.266f)
        curveTo(56.658f, 53.923f, 55.987f, 55.423f, 54.901f, 56.508f)
        curveTo(53.815f, 57.594f, 52.315f, 58.266f, 50.658f, 58.266f)
        lineTo(42.038f, 58.266f)
        lineTo(42.038f, 49.112f)
        curveTo(42.038f, 47.455f, 41.366f, 45.955f, 40.28f, 44.869f)
        curveTo(39.195f, 43.783f, 37.695f, 43.112f, 36.038f, 43.112f)
        lineTo(33.962f, 43.112f)
        curveTo(32.305f, 43.112f, 30.805f, 43.783f, 29.72f, 44.869f)
        curveTo(28.634f, 45.955f, 27.962f, 47.455f, 27.962f, 49.112f)
        lineTo(27.962f, 58.266f)
        lineTo(19.342f, 58.266f)
        curveTo(17.685f, 58.266f, 16.185f, 57.594f, 15.099f, 56.508f)
        curveTo(14.013f, 55.423f, 13.342f, 53.923f, 13.342f, 52.266f)
        lineTo(13.342f, 30.503f)
        curveTo(13.342f, 29.645f, 13.709f, 28.827f, 14.352f, 28.258f)
        lineTo(32.348f, 12.315f)
        curveTo(33.105f, 11.645f, 34.052f, 11.31f, 35f, 11.31f)
        close()
      }
    }.build()

    return _HomeOutlined!!
  }

@Suppress("ObjectPropertyName")
private var _HomeOutlined: ImageVector? = null

@Preview
@Composable
private fun HomeOutlinedPreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.HomeOutlined, contentDescription = null)
  }
}
