package com.example.shorts.ui.node.dialog.edit_wallet_address

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import cafe.adriel.lyricist.LocalStrings
import com.example.shorts.R
import com.example.shorts.foundation.guia.DialogNode
import com.example.shorts.foundation.guia.GlobalNavigator
import com.example.shorts.foundation.kermit.debugLog
import com.example.shorts.foundation.mmkv.WalletKvStore
import com.example.shorts.foundation.toast.showToast
import com.example.shorts.ui.common.button.GradientButton
import com.example.shorts.ui.common.button.GradientColors
import com.example.shorts.ui.theme.AppTheme
import com.roudikk.guia.core.Dialog
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.extensions.currentKey
import com.roudikk.guia.extensions.pop
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import org.koin.compose.koinInject


private const val TAG = "EditWalletAddressDialog"

private fun isValidPayPalAccount(input: String): Boolean {
  val trimmedInput = input.trim()

  // Email format validation
  val emailPattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"

  // Phone number validation (supports various formats)
  val phonePattern = "^[+]?[1-9]\\d{1,14}$|^[0-9]{10,15}$"

  // @username format validation
  val usernamePattern = "^@[a-zA-Z0-9._-]{3,30}$"

  // PayPal.me link validation
  val paypalMePattern = "^(https?://)?(www\\.)?paypal\\.me/[a-zA-Z0-9._-]{3,30}(/.*)?$"

  return trimmedInput.matches(emailPattern.toRegex()) ||
    trimmedInput.matches(phonePattern.toRegex()) ||
    trimmedInput.matches(usernamePattern.toRegex()) ||
    trimmedInput.matches(paypalMePattern.toRegex(RegexOption.IGNORE_CASE))
}


@Parcelize
class EditWalletAddressDialogNode(
  @IgnoredOnParcel private val confirmAction: () -> Unit = {
    GlobalNavigator.tryTransaction {
      if (currentKey is EditWalletAddressDialogNode) {
        pop()
      }
    }
  }
) : DialogNode("edit_wallet_address") {

  @Composable
  override fun Content(
    navigator: Navigator,
    dialog: Dialog?
  ) {
    val strings = LocalStrings.current
    val walletKvStore: WalletKvStore = koinInject()
    val walletAddress by walletKvStore.walletAddress.collectAsState()

    val isInputError = remember { mutableStateOf(false) }

    val onSaveWalletAddress: (input: String) -> Boolean = remember {
      { input ->
        if (isValidPayPalAccount(input)) {
          walletKvStore.walletAddress.value = input
          true
        } else {
          showToast { strings.invalidPaypalAccountError }
          isInputError.value = true
          false
        }
      }
    }

    EditWalletAddressDialog(
      onDismiss = navigator::pop,
      onConfirm = {
        val saveSuccessful = onSaveWalletAddress(it)
        if (saveSuccessful) {
          confirmAction()
        }
      },
      initialInput = walletAddress,
      isInputError = isInputError,
    )
  }

}


@Composable
fun EditWalletAddressDialog(
  onDismiss: () -> Unit,
  onConfirm: (String) -> Unit,
  initialInput: String = "",
  isInputError: MutableState<Boolean>
) {
  val strings = LocalStrings.current
  var input by remember { mutableStateOf(initialInput) }

  Dialog(
    onDismissRequest = onDismiss,
  ) {
    Card(
      modifier = Modifier.fillMaxWidth(),
      shape = RoundedCornerShape(16.dp),
      colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
      Column(
        modifier = Modifier
          .fillMaxWidth()
          .padding(vertical = 24.dp, horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
      ) {
        Row(
          modifier = Modifier.fillMaxWidth(),
          horizontalArrangement = Arrangement.SpaceBetween,
          verticalAlignment = Alignment.CenterVertically
        ) {
          Spacer(modifier = Modifier.width(24.dp))

          Text(
            text = strings.editInformation,
            color = Color.White,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium
          )

          IconButton(
            onClick = onDismiss,
            modifier = Modifier.size(24.dp)
          ) {
            Icon(
              imageVector = Icons.Rounded.Close,
              contentDescription = "Close",
              tint = Color.White.copy(alpha = 0.7f)
            )
          }
        }

        Spacer(modifier = Modifier.height(24.dp))

        Box(
          modifier = Modifier
            .fillMaxWidth()
            .background(
              Color.White,
              RoundedCornerShape(12.dp)
            )
            .padding(16.dp),
          contentAlignment = Alignment.Center
        ) {
          Image(
            painter = painterResource(id = R.drawable.img_paypal),
            contentDescription = "PayPal",
            modifier = Modifier.height(48.dp),
            contentScale = ContentScale.Crop
          )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
          text = strings.paypalDetailsPrompt,
          color = Color.White.copy(alpha = 0.8f),
          fontSize = 12.sp,
          lineHeight = 16.sp
        )

        Spacer(modifier = Modifier.height(16.dp))


        val border = if (isInputError.value) {
          BorderStroke(1.dp, MaterialTheme.colorScheme.error)
        } else {
          BorderStroke(0.dp, Color.Transparent)
        }

        Box(
          modifier = Modifier
            .fillMaxWidth()
            .background(
              color = MaterialTheme.colorScheme.surfaceVariant,
              shape = RoundedCornerShape(8.dp)
            )
            .border(border = border, shape = RoundedCornerShape(8.dp))
            .padding(12.dp)
        ) {
          BasicTextField(
            value = input,
            onValueChange = {
              input = it
              isInputError.value = false
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            textStyle = TextStyle(
              fontSize = 13.sp,
              lineHeight = 16.sp,
              color = Color.White
            ),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions { onConfirm(input) },
            cursorBrush = SolidColor(Color.White),
            decorationBox = { innerTextField ->
              if (input.isEmpty()) {
                Text(
                  text = strings.enterPaypalAccountPlaceholder,
                  color = Color.White.copy(alpha = 0.5f),
                  fontSize = 13.sp,
                  lineHeight = 16.sp
                )
              }
              innerTextField()
            }
          )
        }

        Spacer(modifier = Modifier.height(24.dp))

        GradientButton(
          text = strings.confirm,
          onClick = { onConfirm(input) },
          modifier = Modifier.fillMaxWidth(),
          gradient = GradientColors.Purple,
          enabled = input.isNotBlank(),
          shape = CircleShape
        )
      }
    }
  }
}


@Preview
@Composable
fun EditWalletAddressDialogPreview() {
  AppTheme {
    EditWalletAddressDialog(
      onDismiss = { },
      onConfirm = { debugLog(tag = TAG) { "Confirm: $it" } },
      initialInput = "<EMAIL>",
      isInputError = remember { mutableStateOf(false) }
    )
  }
}