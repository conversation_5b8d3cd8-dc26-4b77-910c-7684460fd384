package com.example.shorts.ui.icon

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.PathData
import androidx.compose.ui.graphics.vector.group
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

val ValkyrieIcons.Welcome: ImageVector
  get() {
    if (_Welcome != null) {
      return _Welcome!!
    }
    _Welcome = ImageVector.Builder(
      name = "Welcome",
      defaultWidth = 196.dp,
      defaultHeight = 196.dp,
      viewportWidth = 196f,
      viewportHeight = 196f
    ).apply {
      path(
        fill = SolidColor(Color(0xFFD8D8D8)),
        fillAlpha = 0f,
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(20f, 0f)
        lineTo(176f, 0f)
        arcTo(20f, 20f, 0f, isMoreThanHalf = false, isPositiveArc = true, 196f, 20f)
        lineTo(196f, 176f)
        arcTo(20f, 20f, 0f, isMoreThanHalf = false, isPositiveArc = true, 176f, 196f)
        lineTo(20f, 196f)
        arcTo(20f, 20f, 0f, isMoreThanHalf = false, isPositiveArc = true, 0f, 176f)
        lineTo(0f, 20f)
        arcTo(20f, 20f, 0f, isMoreThanHalf = false, isPositiveArc = true, 20f, 0f)
        close()
      }
      path(
        fill = Brush.linearGradient(
          colorStops = arrayOf(
            0f to Color(0xFFD364FF),
            1f to Color(0xFF844FDD)
          ),
          start = Offset(134.85f, 111.5f),
          end = Offset(40.27f, 111.5f)
        ),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(64.26f, 62.46f)
        lineTo(24.22f, 151.89f)
        curveTo(21.08f, 158.9f, 24.21f, 167.14f, 31.21f, 170.28f)
        curveTo(34.15f, 171.6f, 37.46f, 171.86f, 40.57f, 171.01f)
        lineTo(135.65f, 144.84f)
        curveTo(146.76f, 141.8f, 153.29f, 130.31f, 150.26f, 119.19f)
        curveTo(149.49f, 116.38f, 148.14f, 113.76f, 146.3f, 111.5f)
        lineTo(95.51f, 58.32f)
        curveTo(89.04f, 50.38f, 77.37f, 49.2f, 69.45f, 55.68f)
        curveTo(67.21f, 57.51f, 65.44f, 59.83f, 64.26f, 62.46f)
        close()
      }
      group(
        clipPathData = PathData {
          moveTo(64.26f, 62.46f)
          lineTo(24.22f, 151.89f)
          curveTo(21.08f, 158.9f, 24.21f, 167.14f, 31.21f, 170.28f)
          curveTo(34.15f, 171.6f, 37.46f, 171.86f, 40.57f, 171.01f)
          lineTo(135.65f, 144.84f)
          curveTo(146.76f, 141.8f, 153.29f, 130.31f, 150.26f, 119.19f)
          curveTo(149.49f, 116.38f, 148.14f, 113.76f, 146.3f, 111.5f)
          lineTo(95.51f, 58.32f)
          curveTo(89.04f, 50.38f, 77.37f, 49.2f, 69.45f, 55.68f)
          curveTo(67.21f, 57.51f, 65.44f, 59.83f, 64.26f, 62.46f)
          close()
        }
      ) {
        path(
          stroke = SolidColor(Color.White),
          strokeLineWidth = 10f,
          strokeLineCap = StrokeCap.Round,
          strokeLineJoin = StrokeJoin.Round,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(60f, 70.5f)
          curveTo(90.67f, 99.31f, 106f, 126.33f, 106f, 151.57f)
        }
      }
      group(
        clipPathData = PathData {
          moveTo(64.26f, 62.46f)
          lineTo(24.22f, 151.89f)
          curveTo(21.08f, 158.9f, 24.21f, 167.14f, 31.21f, 170.28f)
          curveTo(34.15f, 171.6f, 37.46f, 171.86f, 40.57f, 171.01f)
          lineTo(135.65f, 144.84f)
          curveTo(146.76f, 141.8f, 153.29f, 130.31f, 150.26f, 119.19f)
          curveTo(149.49f, 116.38f, 148.14f, 113.76f, 146.3f, 111.5f)
          lineTo(95.51f, 58.32f)
          curveTo(89.04f, 50.38f, 77.37f, 49.2f, 69.45f, 55.68f)
          curveTo(67.21f, 57.51f, 65.44f, 59.83f, 64.26f, 62.46f)
          close()
        }
      ) {
        path(
          stroke = SolidColor(Color.White),
          strokeLineWidth = 10f,
          strokeLineCap = StrokeCap.Round,
          strokeLineJoin = StrokeJoin.Round,
          pathFillType = PathFillType.EvenOdd
        ) {
          moveTo(41f, 111.5f)
          curveTo(61.6f, 124.32f, 70.5f, 142.38f, 67.7f, 165.69f)
        }
      }
      path(
        stroke = SolidColor(Color(0xFFCD63FC)),
        strokeLineWidth = 7f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(134f, 88.5f)
        curveTo(135.67f, 83.28f, 138.53f, 79.94f, 142.57f, 78.51f)
        curveTo(148.63f, 76.35f, 150.37f, 78.75f, 153.66f, 80.11f)
        curveTo(156.96f, 81.48f, 162.13f, 82.46f, 166.08f, 80.11f)
        curveTo(170.03f, 77.77f, 172f, 73.7f, 172f, 70.5f)
      }
      path(
        fill = SolidColor(Color(0xFFF92E72)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(161f, 102.5f)
        moveToRelative(-5f, 0f)
        arcToRelative(5f, 5f, 0f, isMoreThanHalf = true, isPositiveArc = true, 10f, 0f)
        arcToRelative(5f, 5f, 0f, isMoreThanHalf = true, isPositiveArc = true, -10f, 0f)
      }
      path(
        fill = SolidColor(Color(0xFFFFC927)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(168f, 51.5f)
        moveToRelative(-5f, 0f)
        arcToRelative(5f, 5f, 0f, isMoreThanHalf = true, isPositiveArc = true, 10f, 0f)
        arcToRelative(5f, 5f, 0f, isMoreThanHalf = true, isPositiveArc = true, -10f, 0f)
      }
      path(
        fill = SolidColor(Color(0xFF52A5FF)),
        strokeLineWidth = 1f,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(96f, 41.5f)
        moveToRelative(-5f, 0f)
        arcToRelative(5f, 5f, 0f, isMoreThanHalf = true, isPositiveArc = true, 10f, 0f)
        arcToRelative(5f, 5f, 0f, isMoreThanHalf = true, isPositiveArc = true, -10f, 0f)
      }
      path(
        stroke = SolidColor(Color(0xFFCD63FC)),
        strokeLineWidth = 7f,
        strokeLineCap = StrokeCap.Round,
        strokeLineJoin = StrokeJoin.Round,
        pathFillType = PathFillType.EvenOdd
      ) {
        moveTo(115.05f, 41.5f)
        curveTo(113.66f, 36.88f, 114.12f, 32.74f, 116.44f, 29.07f)
        curveTo(119.92f, 23.56f, 122.42f, 24.44f, 125.89f, 23.48f)
        curveTo(129.36f, 22.51f, 132.62f, 20.67f, 134.31f, 16.33f)
        curveTo(135.99f, 12f, 135.99f, 7.95f, 134f, 5.39f)
      }
    }.build()

    return _Welcome!!
  }

@Suppress("ObjectPropertyName")
private var _Welcome: ImageVector? = null

@Preview
@Composable
private fun WelcomePreview() {
  Box(modifier = Modifier.padding(12.dp)) {
    Image(imageVector = ValkyrieIcons.Welcome, contentDescription = null)
  }
}
