package com.example.shorts.initialize

import android.content.Context
import androidx.startup.Initializer
import com.example.shorts.foundation.coroutine.CoroutineModule
import org.koin.android.ext.koin.androidContext
import org.koin.ksp.generated.defaultModule
import org.koin.ksp.generated.module
import org.koin.core.context.startKoin

class KoinInitializer: Initializer<Unit> {
  override fun create(context: Context) {
    startKoin {
      androidContext(context)
      modules(
        defaultModule,
        CoroutineModule().module,
//          DbModule().module,
//        _koin_bi_module
      )
    }
  }

  override fun dependencies(): List<Class<out Initializer<*>?>?> = listOf(MMKVInitializer::class.java)
}