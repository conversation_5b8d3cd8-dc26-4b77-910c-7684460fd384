package com.example.shorts

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import com.example.shorts.foundation.guia.FadeTransitions
import com.example.shorts.foundation.guia.HorizontalSlideTransitions
import com.example.shorts.ui.node.screen.splash.SplashNode
import com.roudikk.guia.core.NavigationKey
import com.roudikk.guia.core.Navigator
import com.roudikk.guia.core.rememberNavigator

@Composable
internal fun appNavigation(): Navigator {
  val initialNode = remember { SplashNode() }

  val navigator = rememberNavigator(initialKey = initialNode) {
    defaultTransition { previous: NavigationKey, new: NavigationKey, isPop ->
      when {
        new is SplashNode -> FadeTransitions.FastFullFade.enterExit
        !isPop -> HorizontalSlideTransitions.Default.enterExit
        else -> HorizontalSlideTransitions.Default.popEnterExit
      }
    }
  }

  return navigator
}
