package com.example.shorts

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import cafe.adriel.lyricist.LocalStrings
import cafe.adriel.lyricist.ProvideStrings
import cafe.adriel.lyricist.rememberStrings
import com.example.shorts.foundation.guia.GlobalNavigator
import com.example.shorts.foundation.lyricist.ConfigureGlobalStringsEffect
import com.example.shorts.foundation.lyricist.runtimeLanguageTagFlow
import com.example.shorts.ui.theme.AppTheme
import com.roudikk.guia.containers.NavContainer

@Composable
fun ComposeAppContainer() {
  val context = LocalContext.current
  val coroutineScope = rememberCoroutineScope()

  val runtimeLanguageTag by runtimeLanguageTagFlow.collectAsState()

  AppTheme {
    ProvideStrings(lyricist = rememberStrings(currentLanguageTag = runtimeLanguageTag)) {

      val navigator = appNavigation()

      navigator.NavContainer(modifier = Modifier.fillMaxSize())

      GlobalNavigator.Register(navigator = navigator, coroutineScope = coroutineScope)
      ConfigureGlobalStringsEffect(LocalStrings.current)
    }
  }
}



//@Composable
//fun BiReportRegister(
//  currentKey: NavigationKey?
//) {
//  val biReporter: BiReporter = koinInject()
//  LaunchedEffect(currentKey) {
//    currentKey?.tag()?.let {
//      launch(Dispatchers.Default) {
//        biReporter.reportPageOnStartEvent(it)
//      }
//    }
//  }
//}

