apply plugin: 'com.android.application'

android {
    compileSdkVersion 33

    defaultConfig {
        applicationId "com.example.dramasdk"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters 'arm64-v8a','armeabi-v7a'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }

        debug{
            minifyEnabled false
        }
    }
    buildFeatures {
        buildConfig = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

repositories {
    flatDir {
        dirs 'libs'
    }
}


dependencies {
    implementation fileTree(include: ['*.aar'], dir: 'libs')
    implementation "androidx.multidex:multidex:2.0.1"
    implementation 'androidx.appcompat:appcompat-resources:1.6.1'
    implementation 'com.google.android.material:material:1.6.0'

    implementation 'com.squareup.okhttp3:okhttp:4.2.1'
    implementation "androidx.viewpager2:viewpager2:1.0.0"
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61'

    // APM
    implementation 'com.pangle.global:pag-apm:2.0.0.7'
    // 播放器
    implementation('com.bytedanceapi:ttsdk-player_standard:1.47.200.2')

    // Pangle广告，便于测试信息流广告
    implementation 'com.pangle.global:pag-sdk:7.2.0.6'

    // Demo里使用GSON测试ShortPlay的序列化兼容性
    implementation 'com.google.code.gson:gson:2.11.0'
}