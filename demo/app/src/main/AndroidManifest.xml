<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.dramasdk">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <application
        android:name=".DemoApp"
        android:extractNativeLibs="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:supportsRtl="true"
        android:theme="@style/Theme.DramaSDK"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name="com.example.dramasdk.MainActivity"
            android:configChanges="keyboardHidden|orientation|screenSize|locale"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity android:name=".DramaPlayActivity" />
        <activity android:name=".SearchActivity" />
        <activity android:name=".WatchHistoryActivity" />
        <activity android:name=".ApiTestActivity" />
        <activity
            android:name=".ChooseIndexDialogActivity"
            android:excludeFromRecents="true"
            android:theme="@style/Theme.AppCompat.Dialog" />
        <activity
            android:name=".ChooseResolutionDialogActivity"
            android:excludeFromRecents="true"
            android:theme="@style/Theme.AppCompat.Dialog" />
    </application>

</manifest>