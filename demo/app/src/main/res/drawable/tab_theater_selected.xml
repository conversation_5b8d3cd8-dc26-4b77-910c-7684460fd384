<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="25dp"
    android:height="24dp"
    android:viewportWidth="25"
    android:viewportHeight="24">
  <group>
    <clip-path
        android:pathData="M0.5,0h24v24h-24z"/>
    <path
        android:pathData="M17,3H12V21H17V3Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.5"
            android:startY="3"
            android:endX="14.5"
            android:endY="21"
            android:type="linear">
          <item android:offset="0" android:color="#FF7E7E7E"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="14.5"
            android:startY="3"
            android:endX="14.5"
            android:endY="21"
            android:type="linear">
          <item android:offset="0" android:color="#FF7E7E7E"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M22,3H17V21H22V3Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="19.5"
            android:startY="3"
            android:endX="19.5"
            android:endY="21"
            android:type="linear">
          <item android:offset="0" android:color="#FF7E7E7E"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="19.5"
            android:startY="3"
            android:endX="19.5"
            android:endY="21"
            android:type="linear">
          <item android:offset="0" android:color="#FF7E7E7E"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M4.75,2.5L10,2.92L8.25,21L3,20.58L4.75,2.5Z"
        android:strokeLineJoin="round"
        android:strokeWidth="2">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="6.5"
            android:startY="2.5"
            android:endX="6.5"
            android:endY="21"
            android:type="linear">
          <item android:offset="0" android:color="#FF7E7E7E"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="6.5"
            android:startY="2.5"
            android:endX="6.5"
            android:endY="21"
            android:type="linear">
          <item android:offset="0" android:color="#FF7E7E7E"/>
          <item android:offset="1" android:color="#FF000000"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.5,9V7.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M14.5,9V7.5"
        android:strokeLineJoin="round"
        android:strokeWidth="2"
        android:fillColor="#00000000"
        android:strokeColor="#ffffff"
        android:strokeLineCap="round"/>
  </group>
</vector>
