<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="34dp"
    android:height="34dp"
    android:viewportWidth="34"
    android:viewportHeight="34">
  <group>
    <clip-path
        android:pathData="M0,0h34v34h-34z"/>
    <path
        android:pathData="M14.827,26.378L16.139,29.606C16.021,31.007 14.637,31.333 13.711,30.621L12.208,26.97"
        android:fillColor="#FD5155"/>
    <path
        android:pathData="M14.827,26.378L16.139,29.606C16.021,31.007 14.637,31.333 13.711,30.621L12.208,26.97"
        android:strokeLineJoin="round"
        android:strokeWidth="0.591954"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M8.055,15.107C8.055,15.107 5.704,15.334 4.456,17.494C3.209,19.655 4.13,23.442 6.483,25.059C9.478,27.117 11.529,24.867 11.529,24.867"
        android:fillColor="#FD5155"/>
    <path
        android:pathData="M8.055,15.107C8.055,15.107 5.704,15.334 4.456,17.494C3.209,19.655 4.13,23.442 6.483,25.059C9.478,27.117 11.529,24.867 11.529,24.867"
        android:strokeLineJoin="round"
        android:strokeWidth="0.591954"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M7.501,21.499C7.501,21.499 5.112,22.694 4.433,22.65C4.433,22.65 7.228,28.581 11.765,24.665C11.765,24.665 9.42,25.094 7.501,21.499Z"
        android:fillColor="#CD2A31"/>
    <path
        android:pathData="M8.467,14.74C8.467,14.74 5.461,17.036 7.503,21.499C9.154,25.109 11.87,24.711 12.86,24.592C13.851,24.473 17.829,24.868 19.056,25.634C20.284,26.401 24.47,27.032 27.135,24.711C29.801,22.39 30.048,16.773 27.52,11.972C24.992,7.17 21.456,3.433 17.427,5.025C13.397,6.616 12.058,11.513 11.113,12.351C10.169,13.188 8.467,14.74 8.467,14.74Z"
        android:fillColor="#FFEA9B"/>
    <path
        android:pathData="M14.366,19.804C14.366,19.804 9.232,20.812 7.502,21.499C7.502,21.499 9.145,24.792 13.745,24.58C17.24,24.419 19.056,25.635 19.056,25.635C19.056,25.635 15.201,21.826 14.366,19.804Z"
        android:fillColor="#FFE06F"/>
    <path
        android:pathData="M16.263,24.841L14.027,26.483C10.486,27.432 10.61,25.763 10.61,25.763L11.766,24.666L13.744,24.581"
        android:fillColor="#CD2A31"/>
    <path
        android:pathData="M8.467,14.74C8.467,14.74 5.461,17.036 7.503,21.499C9.154,25.109 11.87,24.711 12.86,24.592C13.851,24.473 17.829,24.868 19.056,25.634C20.284,26.401 24.47,27.032 27.135,24.711C29.801,22.39 30.048,16.773 27.52,11.972C24.992,7.17 21.456,3.433 17.427,5.025C13.397,6.616 12.058,11.513 11.113,12.351C10.169,13.188 8.467,14.74 8.467,14.74Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M8.055,15.107C8.055,15.107 5.704,15.334 4.456,17.494C3.209,19.655 4.13,23.442 6.483,25.059C9.478,27.117 11.529,24.867 11.529,24.867"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M13.735,26.656L15.385,30.839C15.385,30.839 14.043,31.431 13.45,29.991C12.858,28.551 12.143,26.732 12.143,26.732L13.735,26.656Z"
        android:fillColor="#CD2A31"/>
    <path
        android:pathData="M15.631,24.841C15.631,24.841 15.625,26.733 12.409,26.851C10.624,26.917 10.522,25.787 10.522,25.787"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M14.827,26.378L16.139,29.606C16.021,31.007 14.637,31.333 13.711,30.621L12.208,26.97"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M17.26,3.399C14.086,4.907 11.99,8.537 12.779,14.753C13.569,20.969 19.368,28.984 26.425,27.24C33.481,25.495 31.737,15.444 29.654,10.67C27.572,5.895 22.679,0.997 17.26,3.399Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.591954"
        android:fillColor="#FD5156"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M15.109,20.534L17.71,18.692L29.153,8.726C29.153,8.726 35.542,21.832 27.736,26.663C19.162,30.593 15.109,20.534 15.109,20.534Z"
        android:fillColor="#CD2A31"/>
    <path
        android:pathData="M26.274,24.285C28.592,23.29 30.745,19.198 27.356,11.729C24.982,6.497 20.702,5.244 18.99,5.917C16.373,6.945 14.483,10.95 16.541,16.742C18.965,23.565 23.955,25.279 26.274,24.285Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.807"
            android:startY="14.342"
            android:endX="15.191"
            android:endY="18.56"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFD84D"/>
          <item android:offset="1" android:color="#FFFFF5CC"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M16.317,16.17C17.662,15.184 20.087,13.163 22.572,12.295C22.572,12.295 24.087,11.576 24.365,13.412C24.365,13.412 24.698,14.745 23.373,15.363C23.373,15.363 25.224,17.409 23.772,19.51C23.772,19.51 22.63,21.386 20.695,21.545L19.95,22.272C18.179,20.725 16.815,18.687 16.317,16.17Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M17.114,15.15C17.114,15.15 18.118,18.494 19.94,19.506C19.94,19.506 21.55,20.292 22.703,18.327C23.531,16.916 21.908,14.941 21.908,14.941L24.366,13.158C24.366,13.158 25.129,14.193 23.219,15.398C23.219,15.398 25.172,16.982 23.774,19.509C22.564,21.695 20.817,21.445 20.817,21.445L19.951,22.271C17.091,20.137 16.233,15.802 16.233,15.802C17.077,15.228 17.114,15.15 17.114,15.15Z"
        android:fillColor="#E5E5E5"/>
    <path
        android:pathData="M25.868,24.184C28.344,23.145 30.745,19.199 27.357,11.729C24.983,6.497 21.491,5.068 18.991,5.917C16.327,6.821 14.483,10.95 16.541,16.742C18.966,23.565 23.541,25.159 25.868,24.184Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M17.26,3.399C14.085,4.907 11.99,8.537 12.779,14.753C13.568,20.969 19.54,29.572 26.423,27.239C33.473,24.85 31.736,15.443 29.653,10.67C27.571,5.896 22.679,0.997 17.26,3.399Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M20.15,12.939C19.885,12.348 19.378,11.744 18.42,11.892C17.981,11.96 17.185,12.381 17.256,13.12C17.304,13.624 17.618,14.566 17.584,15.093"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M20.15,12.939C19.885,12.348 19.378,11.744 18.42,11.892C17.981,11.96 17.185,12.381 17.256,13.12C17.304,13.624 17.618,14.566 17.584,15.093"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M16.482,15.989C18.367,14.566 20.888,12.828 21.614,12.479C22.339,12.131 23.437,11.561 24.181,12.487C24.858,13.329 24.708,14.193 24.137,14.677C23.921,14.859 23.619,15.068 23.219,15.399C23.219,15.399 25.226,17.409 23.774,19.51C23.774,19.51 22.753,21.287 20.817,21.447C20.817,21.447 20.272,21.877 19.952,22.272"
        android:strokeLineJoin="round"
        android:strokeWidth="0.922967"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M24.479,9.705C25.07,9.218 25.899,9.102 26.566,9.37L26.567,9.371C27.107,8.361 28.451,7.893 29.475,8.415C30.084,8.726 30.423,9.354 30.363,10.023C30.33,10.391 30.195,10.658 30.064,11.071C29.908,11.56 28.818,13.242 28.818,13.242C28.59,13.872 27.999,14.167 27.488,13.838C27.445,13.899 25.559,13.383 24.778,12.825C23.99,12.264 23.194,10.764 24.479,9.705Z"
        android:fillColor="#E15A4C"/>
    <path
        android:pathData="M26.616,9.424C26.616,9.424 24.634,8.75 23.812,10.776C23.488,11.575 23.919,12.232 24.447,12.639C25.352,13.337 26.642,13.505 27.487,13.838C28.611,14.281 29.004,13.033 29.004,13.033C29.004,13.033 25.493,12.308 25.4,10.888C25.317,9.594 26.616,9.424 26.616,9.424Z"
        android:fillColor="#DB4030"/>
    <path
        android:pathData="M24.48,9.705C25.071,9.218 25.9,9.101 26.567,9.37L26.568,9.37C27.108,8.361 28.452,7.893 29.476,8.415C30.085,8.726 30.627,9.626 30.288,10.687C29.936,11.787 28.751,13.425 28.751,13.425C28.371,13.925 28.299,14.096 27.695,13.949C27.695,13.949 25.643,13.487 24.734,12.905C23.919,12.38 23.195,10.764 24.48,9.705Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.739942"
        android:fillColor="#00000000"
        android:strokeColor="#2B2318"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M28.746,9.976C29.002,9.845 29.111,9.545 28.989,9.306C28.867,9.067 28.56,8.979 28.304,9.11C28.048,9.24 27.939,9.54 28.061,9.779C28.183,10.019 28.489,10.107 28.746,9.976Z"
        android:fillColor="#ffffff"/>
  </group>
</vector>
