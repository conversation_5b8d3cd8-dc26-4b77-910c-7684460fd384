<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="38dp"
        android:layout_marginRight="38dp"
        app:cardCornerRadius="16dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white">

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_cover"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="40dp"
                app:cardElevation="0dp">

                <ImageView
                    android:id="@+id/iv_cover"
                    android:layout_width="186dp"
                    android:layout_height="256dp"
                    android:scaleType="centerCrop" />
            </androidx.cardview.widget.CardView>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:layout_alignLeft="@id/cv_cover"
                android:layout_alignTop="@id/cv_cover"
                android:layout_marginLeft="-10dp"
                android:layout_marginTop="-10dp"
                android:background="@drawable/bg_fff8c9_round"
                android:gravity="center_vertical"
                android:paddingLeft="25dp"
                android:paddingRight="12dp"
                android:text="Just Viewed"
                android:textColor="#2B2318"
                android:textSize="12sp" />

            <ImageView
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_alignLeft="@id/cv_cover"
                android:layout_alignTop="@id/cv_cover"
                android:layout_marginLeft="-20dp"
                android:layout_marginTop="-20dp"
                android:src="@drawable/speaker" />

            <ImageView
                android:id="@+id/tv_close"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_alignParentRight="true"
                android:layout_marginTop="16dp"
                android:layout_marginRight="16dp"
                android:src="@drawable/ic_close" />

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/cv_cover"
                android:layout_marginTop="8dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxLines="1"
                android:textColor="#192734"
                android:textSize="18sp" />

            <TextView
                android:id="@+id/tv_desc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/tv_title"
                android:gravity="center"
                android:textColor="#7f192734"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tv_go_to_watch"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:layout_below="@id/tv_desc"
                android:layout_marginLeft="24dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="24dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/bg_round_black"
                android:gravity="center"
                android:text="Continue to watch"
                android:textColor="@android:color/white"
                android:textSize="16sp" />


        </RelativeLayout>

    </androidx.cardview.widget.CardView>
</FrameLayout>