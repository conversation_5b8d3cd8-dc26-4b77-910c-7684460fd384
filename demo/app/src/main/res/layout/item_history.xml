<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingLeft="2dp"
    android:paddingRight="2dp"
    android:paddingBottom="12dp">

    <com.example.dramasdk.RatioFrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:ratio="0.74">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

    </com.example.dramasdk.RatioFrameLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="@android:color/black" />

    <TextView
        android:id="@+id/tv_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="@android:color/black" />
</LinearLayout>