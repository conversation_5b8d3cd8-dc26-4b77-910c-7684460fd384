<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="3dp"
    android:paddingBottom="3dp">

    <com.example.dramasdk.RatioFrameLayout
        android:id="@+id/cover_layout"
        android:layout_width="50dp"
        android:layout_height="74dp"
        android:layout_alignParentLeft="true"
        android:layout_alignParentTop="true">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />
    </com.example.dramasdk.RatioFrameLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="8dp"
        android:layout_toRightOf="@id/cover_layout"
        android:maxLines="1"
        android:text="title"
        android:textColor="@android:color/black"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/tv_category"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_alignLeft="@id/tv_title"
        android:textColor="@android:color/black" />

    <TextView
        android:id="@+id/tv_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_title"
        android:layout_toRightOf="@id/tv_category"
        android:maxLines="1"
        android:textColor="#7f192734"
        android:textSize="10sp" />
</RelativeLayout>