<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical"
    android:paddingLeft="50dp"
    android:paddingRight="50dp">

    <ImageView
        android:layout_width="328dp"
        android:layout_height="104dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/dialog_header" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-20dp"
        app:cardBackgroundColor="#000000"
        app:cardCornerRadius="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:paddingTop="24dp"
            android:paddingBottom="20dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:id="@+id/tv_title"
                android:text="More exciting stories to unlock"
                android:textColor="#923107"
                android:textSize="17sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="25dp"
                android:layout_marginTop="4dp"
                android:id="@+id/tv_desc"
                android:layout_marginRight="25dp"
                android:layout_marginBottom="32dp"
                android:gravity="center"
                android:lineSpacingExtra="4dp"
                android:text="By watching one rewards ad, you can unlock 5 more episodes"
                android:textColor="#BF161823"
                android:textSize="14sp" />


            <TextView
                android:id="@+id/btn_ok"
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:layout_marginLeft="25dp"
                android:layout_marginRight="25dp"
                android:layout_weight="1"
                android:background="@drawable/bg_e7601f_round"
                android:gravity="center"
                android:shadowColor="#FFEBC5"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="4"
                android:text="Watch rewards ad"
                android:textColor="@android:color/white"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/btn_cancel"
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:layout_marginLeft="25dp"
                android:layout_marginTop="8dp"
                android:layout_marginRight="25dp"
                android:layout_weight="1"
                android:background="@drawable/bg_e88259_round"
                android:gravity="center"
                android:text="Decline the benefit"
                android:textColor="@android:color/white"
                android:textSize="15sp" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>