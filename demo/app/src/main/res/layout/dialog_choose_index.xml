<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white">


        <androidx.cardview.widget.CardView
            android:id="@+id/cv_cover"
            android:layout_width="61dp"
            android:layout_height="82dp"
            android:layout_alignParentLeft="true"
            android:layout_alignParentTop="true"
            android:layout_marginLeft="16dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp">

            <ImageView
                android:id="@+id/iv_cover"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop" />
        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/tv_shortplay_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@id/cv_cover"
            android:layout_marginLeft="7dp"
            android:layout_toRightOf="@id/cv_cover"
            android:textColor="#8F949C"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/tv_shortplay_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/tv_shortplay_desc"
            android:layout_alignLeft="@id/tv_shortplay_desc"
            android:layout_marginBottom="6dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="#23272E"
            android:textSize="16sp" />

        <View
            android:id="@+id/view_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_below="@id/cv_cover"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:alpha="0.1"
            android:background="#192734" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/view_divider"
            android:background="@android:color/white"
            app:tabIndicatorHeight="0dp"
            app:tabMode="fixed" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tab_layout"
            android:paddingLeft="16dp"
            android:paddingRight="16dp"
            android:paddingBottom="16dp" />

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="18dp"
            android:layout_marginRight="18dp"
            android:src="@drawable/arrow_down" />

    </RelativeLayout>
</FrameLayout>