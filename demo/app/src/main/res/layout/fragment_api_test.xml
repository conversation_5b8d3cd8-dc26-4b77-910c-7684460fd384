<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <Button
            android:id="@+id/btn_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="短剧列表" />

        <Button
            android:id="@+id/btn_stream"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="短剧滑滑流" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="查询" />

        <Button
            android:id="@+id/btn_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="按关键词查询短剧" />

        <Button
            android:id="@+id/btn_search_by_tag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="按标签查询短剧" />

        <Button
            android:id="@+id/btn_get_category_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="分类列表" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="操作" />

        <Button
            android:id="@+id/btn_set_collect_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="修改收藏状态" />

        <Button
            android:id="@+id/btn_set_like_status"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="修改点赞状态" />

        <TextView
            android:id="@+id/tv_check_play_status"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:text="检查当前地区是否支持播放..." />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_about"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="20dp"
        android:gravity="center" />
</FrameLayout>