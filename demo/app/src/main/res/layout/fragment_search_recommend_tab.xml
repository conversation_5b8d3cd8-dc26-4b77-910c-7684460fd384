<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="18dp">


    <TextView
        android:id="@+id/title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Hot"
        android:textColor="#192734"
        android:textSize="18sp" />


    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rlv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.example.dramasdk.DataLoadingTipView
            android:id="@+id/data_loading_tip_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </FrameLayout>
</LinearLayout>