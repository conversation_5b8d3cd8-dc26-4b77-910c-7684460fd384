<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="8dp">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="标签ID" />

            <EditText
                android:id="@+id/et_tag_id"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="number"
                android:text="1" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分页索引" />

            <EditText
                android:id="@+id/et_page_index"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="number"
                android:text="1" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="分页大小" />

            <EditText
                android:id="@+id/et_page_count"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:inputType="number"
                android:text="20" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_choose_language"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="4dp"
            android:text="点击切换语言" />

        <Button
            android:id="@+id/btn_do_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="搜索" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rlv"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</FrameLayout>