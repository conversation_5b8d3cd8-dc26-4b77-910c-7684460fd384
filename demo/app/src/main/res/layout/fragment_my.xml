<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingLeft="16dp"
    android:paddingTop="16dp"
    android:paddingRight="16dp">

    <Button
        android:id="@+id/tv_set_language"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="设置视频语言"
        android:textSize="16sp" />

    <Button
        android:id="@+id/tv_watch_history"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_set_language"
        android:text="观看历史"
        android:textSize="16sp" />

    <Button
        android:id="@+id/tv_api_test"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_watch_history"
        android:text="API测试"
        android:textSize="16sp" />

    <TextView
        android:id="@+id/tv_sdk_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="50dp"
        android:textColor="#7f000000" />
</RelativeLayout>