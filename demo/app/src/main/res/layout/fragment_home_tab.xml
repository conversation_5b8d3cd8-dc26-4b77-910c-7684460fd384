<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:scaleType="fitXY"
        android:src="@drawable/bg_header" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="5dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/search_entry"
            android:layout_width="match_parent"
            android:layout_height="32dp"
            android:layout_marginLeft="16dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/bg_round_white"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginLeft="4dp"
                android:src="@drawable/ic_search" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:text="Search"
                android:textColor="#192734"
                android:textSize="14sp" />
        </LinearLayout>


        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/home_tab_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@android:color/transparent"
                        app:tabIndicatorHeight="0dp"
                        app:tabMode="scrollable"
                        app:tabTextColor="@android:color/black" />

                    <androidx.viewpager2.widget.ViewPager2
                        android:id="@+id/home_view_pager"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </LinearLayout>

                <com.example.dramasdk.DataLoadingTipView
                    android:id="@+id/data_loading_tip_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </FrameLayout>

            <LinearLayout
                android:id="@+id/last_watch_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:background="@drawable/bg_round_alpha"
                android:orientation="horizontal"
                android:paddingLeft="16dp"
                android:paddingTop="4dp"
                android:paddingBottom="4dp"
                android:visibility="gone">

                <androidx.cardview.widget.CardView
                    android:layout_width="30dp"
                    android:layout_height="38dp"
                    android:layout_gravity="center_vertical">

                    <ImageView
                        android:id="@+id/tv_last_watch_cover"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:scaleType="centerCrop" />
                </androidx.cardview.widget.CardView>

                <TextView
                    android:id="@+id/tv_last_watch_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="6dp"
                    android:layout_marginRight="6dp"
                    android:maxLines="1"
                    android:text="TitleTitleTitleTitleTitleTitleTitleTitle"
                    android:textColor="#192734"
                    android:textSize="13sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:background="@drawable/bg_round_red_circle_empty"
                    android:paddingLeft="2dp"
                    android:paddingRight="2dp"
                    android:text="Just viewed"
                    android:textColor="#FC6E48"
                    android:textSize="10sp" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <ImageView
                    android:layout_width="22dp"
                    android:layout_height="22dp"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/last_play" />

                <ImageView
                    android:id="@+id/iv_close_last_watch"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="10dp"
                    android:paddingBottom="16dp"
                    android:src="@drawable/ic_close" />
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>

</FrameLayout>