<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="40dp"
    tools:ignore="HardcodedText">

    <ImageView
        android:id="@+id/iv_listitem_icon"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentStart="true"
        android:layout_alignParentLeft="true"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:src="@drawable/tt_ad_logo" />


    <TextView
        android:id="@+id/tv_listitem_ad_desc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/iv_listitem_icon"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="3dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="3dp"
        android:ellipsize="end"
        android:lineSpacingMultiplier="1"
        android:maxLines="2"
        android:singleLine="false"
        android:text="pangle"
        android:textColor="@android:color/black"
        android:textSize="18sp" />

    <RelativeLayout
        android:id="@+id/ad_title_creative_btn_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="2dp">

        <RelativeLayout
            android:id="@+id/tt_ad_logo"
            android:layout_width="40dp"
            android:layout_height="18dp"
            android:layout_centerVertical="true"
            android:gravity="center_vertical" />

        <TextView
            android:id="@+id/tv_listitem_ad_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:layout_marginLeft="5dp"
            android:layout_toStartOf="@+id/tt_creative_btn"
            android:layout_toLeftOf="@+id/tt_creative_btn"
            android:layout_toEndOf="@+id/tt_ad_logo"
            android:layout_toRightOf="@+id/tt_ad_logo"
            android:ellipsize="end"
            android:gravity="center_vertical"
            android:maxLength="14"
            android:singleLine="true"
            android:text="this is title....."
            android:textSize="18sp" />


        <TextView
            android:id="@+id/tt_creative_btn"
            android:layout_width="90dp"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="3dp"
            android:layout_marginLeft="3dp"
            android:layout_marginEnd="8dp"
            android:layout_marginRight="8dp"
            android:background="@drawable/btn_bg_creative"
            android:ellipsize="end"
            android:gravity="center"
            android:lines="1"
            android:padding="4dp"
            android:text="pangle"
            android:textColor="#B22222"
            android:textSize="14sp" />
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/iv_listitem_video"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/ad_title_creative_btn_layout"
        android:layout_below="@id/tv_listitem_ad_desc"
        android:layout_marginStart="10dp"
        android:layout_marginLeft="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginRight="10dp"
        android:scaleType="centerCrop" />

</RelativeLayout>