<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_data_size"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@android:color/darker_gray"
            android:textSize="12sp"
            android:visibility="gone" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rlv"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <TextView
                android:id="@+id/tv_retry"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="点击重试"
                android:textColor="@android:color/black"
                android:visibility="gone" />
        </FrameLayout>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/loading_bar"
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_gravity="center"
        android:indeterminate="true" />

</FrameLayout>