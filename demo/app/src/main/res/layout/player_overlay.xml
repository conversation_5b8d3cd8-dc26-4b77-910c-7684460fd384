<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|top"
        android:layout_marginTop="16dp"
        android:id="@+id/iv_more"
        android:layout_marginRight="16dp"
        android:src="@drawable/config_more" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_overlay_drama_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="32dp"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_overlay_drama_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="32dp"
            android:ellipsize="end"
            android:maxLines="3"
            android:textColor="#e6FFFFFF"
            android:textSize="12sp" />

        <SeekBar
            android:id="@+id/sb_overlay"
            style="@style/Widget.AppCompat.SeekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="0dp"
            android:paddingRight="0dp" />

        <LinearLayout
            android:id="@+id/ll_choose_index"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginBottom="4dp"
            android:background="@drawable/bg_black_round_2dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="16dp"
            android:paddingRight="16dp">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginRight="8dp"
                android:src="@drawable/choose_index" />

            <TextView
                android:id="@+id/tv_overlay_choose_index_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:alpha="0.8"
                android:text="45 Eps XXX"
                android:textColor="@android:color/white"
                android:textSize="12sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:alpha="0.8"
                android:text="Select >"
                android:textColor="@android:color/white"
                android:textSize="12sp" />
        </LinearLayout>
    </LinearLayout>
</merge>