<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical"
    android:paddingLeft="50dp"
    android:paddingRight="50dp">

    <ImageView
        android:layout_width="328dp"
        android:layout_height="104dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/dialog_header" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-20dp"
        app:cardBackgroundColor="#000000"
        app:cardCornerRadius="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:paddingBottom="20dp">

            <LinearLayout
                android:id="@+id/ll_lottery_tip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="14dp">

                    <ImageView
                        android:id="@+id/iv_gift"
                        android:layout_width="85dp"
                        android:layout_height="85dp"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/gift" />

                    <TextView
                        android:id="@+id/tv_main_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentLeft="true"
                        android:layout_marginTop="24dp"
                        android:layout_toLeftOf="@id/iv_gift"
                        android:gravity="center"
                        android:text="Watch ad to unlock"
                        android:textColor="#923107"
                        android:textSize="24sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/tv_main_title"
                        android:layout_alignLeft="@id/tv_main_title"
                        android:layout_marginTop="6dp"
                        android:background="@drawable/bg_de5d00_round"
                        android:paddingLeft="8dp"
                        android:paddingRight="8dp"
                        android:text="1 Episode at least"
                        android:textColor="#DE5D00"
                        android:textSize="14sp" />
                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="14dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="14dp"
                    android:orientation="horizontal">

                    <androidx.cardview.widget.CardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#FADF98"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="2dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_fef3f1_round"
                                android:elevation="1dp"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp"
                                android:src="@drawable/coupon" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:paddingTop="4dp"
                                android:paddingBottom="4dp"
                                android:text="Unlock 1 ep"
                                android:textColor="#681109"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="8dp"
                        android:layout_marginRight="8dp"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#FADF98"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_fef3f1_round"
                                android:elevation="1dp"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp"
                                android:src="@drawable/coupon" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:paddingTop="4dp"
                                android:paddingBottom="4dp"
                                android:text="Unlock 6 eps"
                                android:textColor="#681109"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        app:cardBackgroundColor="#FADF98"
                        app:cardCornerRadius="12dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/bg_fef3f1_round"
                                android:elevation="1dp"
                                android:paddingLeft="10dp"
                                android:paddingRight="10dp"
                                android:src="@drawable/coupon" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:paddingTop="4dp"
                                android:paddingBottom="4dp"
                                android:text="Unlock all"
                                android:textColor="#681109"
                                android:textSize="12sp" />
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>
            </LinearLayout>


            <!--结果-->
            <LinearLayout
                android:id="@+id/ll_lottery_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="You won the reward!"
                    android:textColor="#923107"
                    android:textSize="24sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/coupon" />

                <TextView
                    android:id="@+id/tv_lottery_result"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="3 episodes unlocked!"
                    android:textColor="#DE5D00"
                    android:textSize="20sp" />
            </LinearLayout>

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="42dp"
                android:layout_marginLeft="25dp"
                android:layout_marginTop="20dp"
                android:layout_marginRight="25dp"
                android:background="@drawable/bg_fa2f13_ffa741_round">

                <TextView
                    android:id="@+id/btn_ok"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawableLeft="@drawable/unlock_play_icon"
                    android:gravity="center"
                    android:text="Watch ad to join(3s)"
                    android:textColor="@android:color/white"
                    android:textSize="16sp" />
            </FrameLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <ImageView
        android:id="@+id/btn_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="16dp"
        android:src="@drawable/close_with_circle" />
</LinearLayout>