<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:orientation="vertical"
    android:paddingLeft="50dp"
    android:paddingRight="50dp">

    <ImageView
        android:layout_width="164dp"
        android:layout_height="52dp"
        android:layout_gravity="center_horizontal"
        android:src="@drawable/dialog_header" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="-2dp"
        app:cardCornerRadius="24dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:paddingLeft="16dp"
            android:paddingTop="65dp"
            android:paddingRight="16dp"
            android:paddingBottom="43dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="32dp"
                android:gravity="center"
                android:text="View ads to unlock"
                android:textColor="#923107"
                android:textSize="16sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_cancel"
                    android:layout_width="0dp"
                    android:layout_height="42dp"
                    android:layout_weight="1"
                    android:background="@drawable/bg_round_red_circle"
                    android:gravity="center"
                    android:shadowColor="#FFEBC5"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="4"
                    android:text="Cancel"
                    android:textColor="#FD6328"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/btn_unlock"
                    android:layout_width="0dp"
                    android:layout_height="42dp"
                    android:layout_marginLeft="8dp"
                    android:layout_weight="1"
                    android:background="@drawable/rectangle_1"
                    android:gravity="center"
                    android:text="Unlock"
                    android:textColor="@android:color/white" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</LinearLayout>