<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingLeft="2dp"
    android:paddingRight="2dp"
    android:paddingBottom="12dp">

    <com.example.dramasdk.RatioFrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:ratio="0.74">

        <ImageView
            android:id="@+id/iv_cover"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:layout_gravity="bottom"
            android:background="@drawable/bg_gradient_gray">

            <TextView
                android:id="@+id/tv_hot_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_centerInParent="true"
                android:layout_marginRight="8dp"
                android:gravity="center_vertical"
                android:text="83k"
                android:textColor="@android:color/white"
                android:textSize="11sp" />

            <ImageView
                android:layout_width="11dp"
                android:layout_height="11dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="2dp"
                android:layout_toLeftOf="@id/tv_hot_value"
                android:src="@drawable/fire" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tag_view"
            android:layout_width="wrap_content"
            android:layout_height="14dp"
            android:layout_gravity="right|top"
            android:gravity="center"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:textColor="@android:color/white"
            android:textSize="9sp" />

    </com.example.dramasdk.RatioFrameLayout>

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="1"
        android:textColor="#192734"
        android:textSize="13sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_category"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gray_round_2dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:paddingLeft="4dp"
            android:paddingRight="4dp"
            android:textColor="#CC192734"
            android:textSize="10sp" />

        <View
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_weight="1" />

        <TextView
            android:id="@+id/tv_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:alpha="0.6"
            android:gravity="center"
            android:textColor="#192734"
            android:textSize="10sp" />

    </LinearLayout>
</LinearLayout>