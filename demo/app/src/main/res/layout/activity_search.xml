<?xml version="1.0" encoding="utf-8"?>

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:scaleType="fitXY"
        android:src="@drawable/bg_header" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:layerType="software"
        android:orientation="vertical"
        android:paddingLeft="16dp"
        android:paddingRight="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="49dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_back" />

            <LinearLayout
                android:id="@+id/search_entry"
                android:layout_width="match_parent"
                android:layout_height="32dp"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:background="@drawable/bg_round_white"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginLeft="4dp"
                    android:src="@drawable/ic_search" />

                <EditText
                    android:id="@+id/et_keyword"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:gravity="center_vertical"
                    android:hint="Search drama name"
                    android:imeOptions="actionSearch"
                    android:singleLine="true"
                    android:textColor="#192734"
                    android:textColorHint="#4c192734"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/search_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="4dp"
                    android:text="Search"
                    android:textColor="@android:color/black" />
            </LinearLayout>
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/search_result_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <LinearLayout
                android:id="@+id/no_content_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="gone">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/no_content" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="No matching content"
                    android:textColor="#7f000000"
                    android:textSize="14sp" />
            </LinearLayout>

            <com.example.dramasdk.DataLoadingTipView
                android:id="@+id/loading_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/recommend_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">


                <RelativeLayout
                    android:id="@+id/search_history_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="left|center_vertical"
                        android:text="History"
                        android:textColor="#192734"
                        android:textSize="16sp" />

                    <ImageView
                        android:id="@+id/btn_clean_history"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/ic_delete" />

                    <HorizontalScrollView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/btn_clean_history"
                        android:layout_marginTop="16dp"
                        android:layout_marginBottom="16dp">

                        <LinearLayout
                            android:id="@+id/search_history_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingLeft="10dp"
                            android:paddingRight="10dp">

                        </LinearLayout>
                    </HorizontalScrollView>
                </RelativeLayout>

                <HorizontalScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <LinearLayout
                        android:id="@+id/fragment_container"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal" />
                </HorizontalScrollView>
            </LinearLayout>
        </FrameLayout>
    </LinearLayout>
</FrameLayout>