// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()

        mavenCentral()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:4.2.0"
    }
}

allprojects {
    repositories {
        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }
        maven {
            url 'https://artifact.bytedance.com/repository/pangle/'
        }

        maven {
            url 'https://artifact.byteplus.com/repository/public/'
        }
        google()

        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}