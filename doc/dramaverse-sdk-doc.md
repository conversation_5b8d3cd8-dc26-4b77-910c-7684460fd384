> **Note：因合规要求，如需测试短剧播放只能通过开启VPN使用SDK支持的国家或地区的节点，否则会导致剧集无法播放或者报10011错误，SDK支持的国家和地区为：中国台湾、泰国、越南、马来西亚、印度尼西亚、菲律宾、新加坡、柬埔寨、日本、韩国、巴西、澳大利亚、新西兰、阿根廷、智利、哥伦比亚、厄瓜多尔、秘鲁、乌拉圭、墨西哥、沙特阿拉伯、阿联酋、土耳其、哈萨克斯坦、巴基斯坦、科威特、伊拉克、卡塔尔、约旦、阿曼、巴林、黎巴嫩、斯里兰卡、阿塞拜疆、白俄罗斯、乌克兰、埃及、南非共和国、摩洛哥、肯尼亚、阿尔及利亚。**
>
> **另外Demo仅供预览使用，我们会限制剧集下发，如果您需要获取所有剧集列表请使用你们自己的正式信息发起请求。**



**问题自查文档：[ 短剧SDK FAQ](https://bytedance.sg.larkoffice.com/docx/Tw1edyAaqoZ6Y7xB3bfl4tWQg6g)**



# 更新日志

## 最新

2025-08-06

> 1700新版本修改如下：
>
> 1. 支持16KB，需升级播放器至implementation 'com.bytedanceapi:ttsdk-player\_standard:**********
>
> 2. 新增API设置收藏状态的接口
>
> 3. 新增短剧滑到顶以及滑到底的回调，具体参考createDetailFragment中的onOverScroll方法

2025-04-27

> **1600新版本修改如下：**
>
> **替换播放域名，因为内部的播放域名调整，原有1600版本以下的SDK版本预计在05/30后无法正常请求短剧信息，辛苦各位尽快安排1600版本的升级工作，避免后续影响线上的正常短剧播放。**

2025-02-13

> 1500新版本修改如下：
>
> 优化播放配置，提升播放效果

2025-01-21

> 1400新版本修改如下：
>
> 新增支持静音能力
>
> 新增支持设置剧集封面占位图
>
> Demo处新增弹框挽留示例
>
> SDK播放失败自动重试能力

2024-12-10

> 1300新版本修改如下：
>
> 新增获取短剧分类标签的接口
>
> 新增倍速播放功能
>
> 新增自定义进度条功能

2024-11-21

> 1200新版本修改如下：
>
> 新增获取短剧列表：按标签获取短剧列表
>
> 优化播放耗时问题

2024-11-07

> 1104新版本修改如下：
>
> 支持播放进度记录和恢复
>
> 修改设置内容语言接口，支持传入list获取多个语言的剧集

2024-10-14

> 1100新版本修改如下：
>
> 新增近期上新短剧列表
>
> 优化搜索短剧的API
>
> 新增手动预加载功能
>
> 新增播放页面点赞收藏功能

## 历史记录

2024-9-10

> 1014新版本修改如下：
>
> 新增播放回调onItemSelected
>
> 新增banner广告

2024-8-9

> 1013新版本修改如下：
>
> 最小支持版本从API 16升到API 21
>
> 增加搜索功能
>
> 新增支持开发者插入广告到播放页剧集中间

2024-4-7

> 1009版本修改如下：
>
> 优化起播耗时
>
> 修复进入播放页后快速退出可能仍播放等问题
>
> 无API变更

2024-3-6

> 1008版本修改如下：
>
> 新增调节进度条高度的API
>
> 新增获取热门短剧的API
>
> 修改广告展示/用户付费收入信息回传的API
>
> 具体API的信息请参照文档标红部分

2024-2-6

> 新增货币单位传值规范
>
> 新增播放失败回调（2/6 1006版本SDK提供）
>
> 新增视频播放前检查视频播放状态接口（2/6 1006版本SDK提供）

2024-2-2

> 调整播放器配置优化起播体验
>
> SDK文案和Demo内容语言列表支持繁体中文
>
> Demo内容语言列表里印尼语代码由id改成in

2024-1-31

> ShortPlay的分类ID由int改成long
>
> 对外增加手动控制沉浸式的API
>
> 解决一些bug

2024-1-26

> 新增必要的maven仓库
>
> 新增手动进入和退出沉浸模式的API

2024-1-24

> 修改shortPlayIds为long类型
>
> 新增隐藏播放页左下角文字描述接口
>
> 新增播放器播放状态回调
>
> 新增常见错误码

2024-1-22

> 新版本即将修改付费信息回传方式
>
> 更新10011问题解决方案

2024-1-19

> “短剧对象字段”：短剧分类之前是String，改成List\<ShortPlayCategory>类型了，因为一部短剧上可能有多个分类；下面也贴上了ShortPlayCategory的字段说明
>
> "创建和显示播放页": 加上了"playSingleItem"这个新增配置项，如果开发者想自己做滑动切剧，需要配置这个来让我们关闭滑动切上下集功能，不然手势有冲突
>
> 新增“播放页支持的API”，列出了播放页支持的API，包括播放、暂停等

2024-1-18

> 更新maven依赖库

2024-1-17

> 更新付费信息回传以及自动播放下一集的API

2024-1-12

> 初版接入文档

# 快速入门

## SDK环境要求

* minSdk >= API 21

* OK HTTP库version>=4.x

* cpu ABI只支持arm64-v8a/armeabi-v7a

  * Example:&#x20;

  ```groovy
  defaultConfig {
      ndk {
          abiFilters 'arm64-v8a','armeabi-v7a'
      }
  }
  ```

## 添加SDK

**Maven方式**

* 现阶段不提供maven接入方式，但是需要添加以下必要依赖

project-level build.gradle

```typescript
allprojects {
    repositories {
        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }
        maven {
            url 'https://artifact.byteplus.com/repository/public/'
        }
    }
}
```

App-level build.gradle

```groovy
implementation 'com.squareup.okhttp3:okhttp:4.2.1'
implementation "androidx.viewpager2:viewpager2:1.0.0"
implementation 'com.github.bumptech.glide:glide:4.16.0'

implementation 'com.google.android.gms:play-services-ads-identifier:18.0.1'

implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.3.61'

// video player
implementation 'com.bytedanceapi:ttsdk-player_standard:**********
```

**AAR方式**

* 联系Dramaverse团队给包

**添加视频播放授权文件**

* 将lic文件放到项目assets目录下即可



## 初始化

建议在Application的onCreate里调用SDK初始化代码，SDK中所有的方法都需要等初始化之后才能调用，在初始化完成之前获取短剧信息均会失败。

```java
PSSDK.Config.Builder builder = new PSSDK.Config.Builder();
builder.appId("xxxx")
        .vodAppId("xxxx")
        .securityKey("xxxxx")
        .licenseAssertPath("xxxxx");
PSSDK.init(this, builder.build(), new PSSDK.PSSDKInitListener() {
    @Override
    public void onInitFinished(boolean success, PSSDK.ErrorInfo errorInfo) {
        if (DEBUG) {
            Log.d(TAG, "onInitFinished() called with: success = [" + success + "], errorInfo = [" + errorInfo + "]");
        }
    }
});
```

可配置参数

|                   | 是否必传 | 含义                                                                                                                                                    |
| ----------------- | ---- | ----------------------------------------------------------------------------------------------------------------------------------------------------- |
| appId             | 是    | 区分应用，所有新app的id都需要由Dramaverse提供，请使用红色部分的id![](images/image.png)                                                                                        |
| vodAppId          | 是    | 点播播放器使用，与byteplus签署获取![](images/image-1.png)                                                                                                          |
| licenseAssertPath | 是    | 视频播放授权文件在assets下的路径，比如根目录下可传abc.lic，每个包名对应的授权文件不一样，与byteplus签署获取                                                                                      |
| securityKey       | 是    | 鉴权用，在你们自己的[ Dramaverse Short Drama Project w/ XXXXXX](https://bytedance.sg.larkoffice.com/docx/BSHFdEuiXo7Bq3xxxVilgfJwg8g)文档中填写橙色部分信息后，由Dramaverse提供 |
| userId            | 否    | 标识开发者应用里的一个用户，便于和Dramaverse埋点数据打通                                                                                                                     |
| debug             | 否    | SDK是否启用DEBUG模式，DEBUG模式下会有更多log输出，默认false                                                                                                              |



## 混淆规则

代码混淆规则由Dramaverse aar里自带了，如需对资源进行混淆请keep "pssdk\_" 相关的资源文件

使用SDK v1.1.0.0及以上版本请新增以下配置：

```c++
-dontwarn android.os.SystemProperties
```



## 合规设置

**该接口用于设置当前用户是否适合观看短剧内容（包括但不限于，用户是否满足观看短剧的最低年龄，用户是否允许个人数据的采集和分享给三方），true表示适合，false表示不适合，false场景下无法获取内容；默认为false。可以在获取剧目数据之前任意时机进行设置。**

```java
PSSDK.setEligibleAudience(true/false);
```

## 设置内容语言

返回的短剧列表均是根据手机语言筛选后的，开发者可手动设置语言，设置后的下一次请求生效。如不设定则默认读取手机的本地语言来使用，语言参考 https://www.loc.gov/standards/iso639-2/php/code\_list.php

```java
/**
 * 设置语言
 *
 * @param subtitleLanguages 视频字幕语言，比如en，具体格式可参考本文档“语言参数格式”一节，可传多个
 */
PSSDK.setContentLanguages(List<String> subtitleLanguages)
```

语言参数格式以及支持的语言如下：

| 语言   | 代码                   |
| ---- | -------------------- |
| 简体中文 | zh\_hans             |
| 繁体中文 | zh\_hant             |
| 英语   | en                   |
| 越南   | vi                   |
| 印尼   | in(id也兼容，但是最好还是使用in) |
| 泰语   | th                   |
| 日语   | ja                   |
| 韩语   | ko                   |
| 葡萄牙语 | pt                   |
| 西班牙语 | es                   |

注意：支持的语言以此列表为主，我们会进行同步更新，另外支持的国家/地区与支持的语言列表不绑定，如我们在柬埔寨支持播放短剧，但是我们暂未支持柬埔寨语。



## 获取短剧列表

> 1. 关于index与count的注意事项：index代表页索引，即第x页数据。count则代表单页有剧集数量。
>
> 如果您现在存在分页需求，每页20条数据，则按照以下顺序依次请求
>
> index = 1 , count = 20
>
> Index = 2, count = 20
>
> ......

### 1. 获取默认列表

> 该列表返回顺序在过一段时间会随机变化

```java
/**
 * 获取短剧列表
 *
 * @param index 页索引，从1开始
 * @param count 请求数量
 */
PSSDK.requestFeedList(pageStartIndex, count, new PSSDK.FeedListResultListener() {

    @Override
    public void onFail(PSSDK.ErrorInfo errorInfo) {
        // 请求失败
    }

    @Override
    public void onSuccess(PSSDK.FeedListLoadResult<ShortPlay> result) {
        // 短剧列表
        List<ShortPlay> dataList = result.dataList;
        // 分页请求，是否还有更多数据
        boolean hasMore = result.hasMore;
        
        // 将数据显示到界面上
    }
});
```



### 2. 获取指定短剧ID或指定分类的列表

该接口的使用范围为：

* 想获取指定分类的短剧对象

* 想获取特定短剧ID的短剧对象

```java
/**
 * 获取指定分类或短剧的列表
 *
 * @param categoryIds  指定分类ID集合，可为null
 * @param shortPlayIds 指定短剧ID的集合，可为null
 * @param index        页索引，从1开始
 * @param count        请求数量
 */
PSSDK.requestFeedListByCategoryIds(List<Long> categoryIds, List<Long> shortPlayIds, int index, int count, FeedListResultListener listener) {
```

> 当categoryIds和shortPlayIds均传null时，效果和requestFeedList一样

类目ID如下：

| 类目ID | 类目名称（中文）                 |
| ---- | ------------------------ |
| 1    | 男频/Male Category         |
| 2    | 女频/Female Category       |
| 4    | 悬疑/Suspense              |
| 5    | 现言/Contemporary Romance  |
| 6    | 古言/Historical Romance    |
| 7    | 军事/Military              |
| 8    | 玄幻/Fantasy               |
| 11   | 喜剧/Comedy                |
| 12   | 动作/Action                |
| 13   | 二次元/Anime                |
| 14   | 其他/Others                |
| 1701 | 逆袭/Comeback              |
| 1702 | 重生/Reborn                |
| 1704 | 复仇/Revenge               |
| 1706 | 霸总/Domineering President |
| 1709 | 战神/God Of War            |
| 1751 | 萌宝/Cute Baby             |
| 1851 | 团宠/Group Favorite        |
| 1901 | 甜宠/Sweet Love            |
| 1902 | 系统/System                |
| 1951 | 虐恋/Sadistic Love         |
| 1952 | 年代/Era                   |
| 1953 | 亲情/Family                |
| 1954 | 体育/Sports                |

### 3. 获取热门短剧列表

目前是按T-1的播放量排序

```java
PSSDK.requestPopularDrama(int index, int count, FeedListResultListener listener);
```



### 4. 获取近期新增短剧列表(1100增加)

```sql
PSSDK.requestNewDrama(int index, int count, FeedListResultListener listener);
```



### 5. 获取近期最多收藏接口（1200增加）

```java
PSSDK.requestDramaByTag(long tagId, int index, int count, FeedListResultListener listener);
```

\*目前只支持传入tagId = 4：近期最多收藏



### 6. 获取分类标签接口（1300版本支持）

```java
/**
 * 获取所有分类列表
 *
 * @param language 分类名语言
 */
PSSDK.requestCategoryList(String language, CategoryListResultListener listener)
```

传入语言参照“设置内容语言”模块



### 短剧对象字段

```java
/**
 * 短剧
 */
public class ShortPlay {
    /**
     * 已完结
     */
    public static final int PROGRESS_STATE_END = 1;
    /**
     * 未完结
     */
    public static final int PROGRESS_STATE_NOT_END = 2;
    /**
     * 短剧ID
     */
    public final long id;
    /**
     * 短剧标题
     */
    public final String title;
    /**
     * 短剧分类
     */
    public final List<ShortPlayCategory> categories = new ArrayList<>();
    /**
     * 总集数
     */
    public final int total;
    /**
     * 完结状态，1完结，2未完结
     */
    public final int progressState;
    /**
     * 短剧描述
     */
    public final String desc;
    /**
     * 封面URL
     */
    public final String coverImage;
       /**
     * 总收藏数
     */
    public int totalCollectCount;
    /**
     * 当前用户是否收藏该剧
     */
    public boolean isCollected;
    
    /**
     * 短剧分类
     */
    public static class ShortPlayCategory {
        public final int id;
        public final String name;

        public ShortPlayCategory(int id, String name) {
            this.id = id;
            this.name = name;
        }
    }
}
```



## 检查视频播放状态

目前SDK仅在指定地区支持播放功能，在非支持地区会播放失败；可以通过此API在进入到播放之前检查本地区是否支持播放

```java
PSSDK.checkPlayStatus(new PSSDK.ServiceCheckResultListener() {
    @Override
    public void onCheckResult(PSSDK.ServiceAvailableResult result) {
        Log.d(TAG, "onCheckResult() called with: result = [" + result + "]");
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                switch (result) {
                    case SUPPORT:
                        Toast.makeText(getApplicationContext(), "当前地区是否支持播放：支持", Toast.LENGTH_SHORT).show();
                        break;
                    case NOT_SUPPORT:
                        Toast.makeText(getApplicationContext(), "当前地区是否支持播放：不支持", Toast.LENGTH_SHORT).show();
                        break;
                    case NETWORK_ERROR:
                        Toast.makeText(getApplicationContext(), "当前地区是否支持播放：网络错误", Toast.LENGTH_SHORT).show();
                        break;
                }
            }
        });
    }
});
```

## 播放短剧

创建和显示播放页

```java
PSSDK.DetailPageConfig.Builder builder = new PSSDK.DetailPageConfig.Builder();
builder
        .displayTextColor(PSSDK.DetailPageConfig.TEXT_POS_TOP_TITLE, Color.RED) // 【可选】设置顶部标题文字颜色
        .displayTextSize(PSSDK.DetailPageConfig.TEXT_POS_TOP_TITLE, 24) // 【可选】设置顶部标题文字大小
        .displayTextFont(PSSDK.DetailPageConfig.TEXT_POS_TOP_TITLE, null) // 【可选】设置顶部标题文字字体
        .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_TITLE, false) // 【可选】隐藏底部标题文字
        .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_DESC, false) // 【可选】隐藏底部描述文字
        .hideLeftTopCloseAndTitle(true, null) // 【可选】隐藏左上角返回按钮和标题，默认显示
        .enableImmersiveMode(10000) // 【可选】播放页无操作xxxms后隐藏文字进入沉浸式模式，默认不启用此功能，启用时可指定时间
        .playSingleItem(false) // 【可选】只播放一集模式，用于在开发者用多个播放页Fragment对象构造滑动切剧场景时，默认false
        .startPlayIndex(10); // 【可选】从xx集开始播放，默认从第1集开始
        .progressBarMarginToBottom(10) // 【可选】设置播放进度条离底部距离，单位dp
detailFragment = PSSDK.createDetailFragment(shortPlay, builder.build(), new PSSDK.ShortPlayDetailPageListener() {
    @Override
    public void onShortPlayPlayed(ShortPlay shortPlay, int index) {
        // 每一集开始播放时回调，可用来记录播放历史
        Log.d(TAG, "onShortPlayPlayed() called with: shortPlay = [" + shortPlay + "], index = [" + index + "]");
    }
    
    @Override
    public void onOverScroll(int direction) {
        //1700版本新增，当视频滑到顶或者滑到底的时候触发
    
    }
    
     @Override
    public boolean onPlayFailed(PSSDK.ErrorInfo errorInfo) {
        //视频播放失败，错误码参考：https://docs.byteplus.com/en/byteplus-vod/docs/error-code
        Log.d(TAG, "onPlayFailed() called with: errorInfo = [" + errorInfo + "]");
        if (errorInfo.code == PSSDK.ErrorInfo.ERROR_CODE_CURRENT_COUNTRY_NOT_SUPPORT) {
            // 当前地区不支持播放，SDK会Toast提示，开发者也可以在此时显示弹窗等更友好的提示
            AlertDialog.Builder dialogBuilder = new AlertDialog.Builder(getActivity());
            dialogBuilder.setMessage("当前地区不支持播放");
            dialogBuilder.create().show();
            // return true表示替换掉SDK内的Toast提示
            return true;
        }
        return false;
    }
      @Override
    public void onItemSelected(int position, ItemType type) {
        // 播放页里视频列表发生变化时回调，type可区分是视频还是广告
    }
    /**
     * 视频播放状态发生变化
     * @param playbackState 见{@link PSSDK#PLAYBACK_STATE_PLAY},{@link PSSDK#PLAYBACK_STATE_PAUSE}
     */
    void onVideoPlayStateChanged(ShortPlay shortPlay, int index, int playbackState);

    /**
     * 视频播放完成
     */
    void onVideoPlayCompleted(ShortPlay shortPlay, int index);

    @Override
    public void onEnterImmersiveMode() {
        // 进入沉浸式模式
        Log.d(TAG, "onEnterImmersiveMode() called");
    }

    @Override
    public void onExitImmersiveMode() {
        // 退出沉浸式模式
        Log.d(TAG, "onExitImmersiveMode() called");
    }

    @Override
    public boolean isNeedBlock(ShortPlay shortPlay, int index) {
        // 询问index集是否锁定，true锁定后则该集无法自动播放，需要通过showAdIfNeed里完成解锁
        // 默认对每一集均会询问，一旦返回false则此播放页不会再询问该集
        Log.d(TAG, "isNeedBlock() called with: shortPlay = [" + shortPlay + "], index = [" + index + "]");
        return false;
    }

    @Override
    public void showAdIfNeed(ShortPlay shortPlay, int index, PSSDK.ShortPlayBlockResultListener listener) {
        // 当isNeedBlock指定index集锁定后，在用户切换到该集时，SDK不会播放视频，同时会调用此回调，可在此时展示激励广告或购买等交互，用户达成后调用listener.onShortPlayUnlocked告知SDK可播放该集
        Log.d(TAG, "showAdIfNeed() called with: shortPlay = [" + shortPlay + "], index = [" + index + "], listener = [" + listener + "]");
        
    }

    @Override
    public void onVideoInfoFetched(ShortPlay shortPlay, int index, PSSDK.VideoPlayInfo videoPlayInfo) {
        // 每一集视频准备好时调用此方法告知本集的视频信息
        Resolution[] resolutions = videoPlayInfo.supportResolutions;
        
    }
});

if(detailFragment!=null){
    // 将播放页展示出来
    getSupportFragmentManager().beginTransaction().add(R.id.fl_container, detailFragment).show(detailFragment).commitNow();
}else{
    // detailFragment对象为空，一般是shortPlay对象为null或内部id/total关键字段为0，正常服务端下发不会有此问题
}
```

可定义样式的文字位置：

* PSSDK.DetailPageConfig.TEXT\_POS\_TOP\_TITLE: 左上角文字，显示“第XX集”，该区域的多语言适配根据手机系统语言来匹配，而非短剧的语言。

* PSSDK.DetailPageConfig.TEXT\_POS\_BOTTOM\_TITLE：左下角标题文字，显示短剧名

* PSSDK.DetailPageConfig.TEXT\_POS\_BOTTOM\_DESC：左下角描述文字，显示“是否已完结 ｜ 共XX集”



## 播放页支持的API

开发者可以通过使用以下API来控制播放、暂停、停止等逻辑。

```groovy
public abstract class ShortPlayFragment extends Fragment {
    /**
     * 播放指定集
     *
     * @param index 第几集
     */
    public abstract void startPlayIndex(int index);

    /**
     * 设置播放分辨率
     */
    public abstract void setResolution(Resolution resolution);

    /**
     * 开始播放
     */
    public abstract void startPlay();

    /**
     * 停止播放
     */
    public abstract void stopPlay();

    /**
     * 暂停播放
     */
    public abstract void pausePlay();
    
     /**
     * 手动进入沉浸式模式，一旦使用手动API，SDK内则不再自动处理
     */
    public abstract void requestStartImmersiveMode();

    /**
     * 手动退出沉浸式模式，一旦使用手动API，SDK内则不再自动处理
     */
    public abstract void requestStopImmersiveMode();
}
```

## 剧集解锁

默认所有短剧和所有剧集均可播放，开发者可以设计短剧前xx集直接观看，后面集数需要观看广告或者购买才能观看。

`boolean isNeedBlock(ShortPlay shortPlay, int index)`

* 开发者需记录用户解锁的短剧和集数情况，在此方法里返回解锁状态，便于告知播放器该集是否可直接观看

* 对于返回false表示没有锁定的剧集，同一个播放界面不会再次询问

`void showAdIfNeed(ShortPlay shortPlay, int index, PSSDK.ShortPlayBlockResultListener listener)`

* SDK发现XX集需要解锁才能看时，会在用户切换到该集时，不自动播放，同时回调此方法，开发者可以在此时展示广告或购买弹窗等，用户达成后，调用`listener.onShortPlayUnlocked()`方法表示该集解锁



## 广告展示/用户付费收入信息回传

```html
// 场景1: 用户在App里看了广告
PSSDK.RevenueInfo revenueInfo = new PSSDK.RevenueInfo(PSSDK.RevenueInfo.RevenueType.IAA, PSSDK.RevenueInfo.CurrencyType.USD);
revenueInfo.revenue(0.1f); // 展示广告带来的收益，如果是CPM相关接口，需要提供CPM/1000的计算结果，单位美元
revenueInfo.adnName("xxx"); // 广告ADN名
revenueInfo.adFormat(PSSDK.RevenueInfo.AdFormat.REWARD_VIDEO); // 广告样式，比如激励视频等
revenueInfo.aboutUnlock(false); // 表示和短剧解锁无关
PSSDK.reportRevenueInfo(revenueInfo);


// 场景2: 用户购买完成解锁短剧
revenueInfo = new PSSDK.UnlockRevenueInfo(PSSDK.UnlockRevenueInfo.RevenueType.IAP, PSSDK.RevenueInfo.CurrencyType.USD);
revenueInfo.revenue(0.1f);// 购买付费金额，单位美元
revenueInfo.aboutUnlock(true); // 表示是和短剧解锁有关
PSSDK.reportRevenueInfo(revenueInfo);
```

```groovy
// 传给SDK进行上报
PSSDK.reportRevenueInfo(unlockRevenueInfo);
```

| 货币单位     | 开发者回传的枚举值（Currency） |
| -------- | ------------------- |
| 美元       | usd                 |
| 越南盾      | vnd                 |
| 人民币      | cny                 |
| 泰铢       | thp                 |
| 马币（马来西亚） | rm                  |
| 印尼盾（印尼）  | idr                 |
| 新加坡元     | sgd                 |



## 选集

开发者可以在界面上自行添加选集入口，用户选择后通过`detailFragment.startPlayIndex(index)`来播放指定集



## 常见错误码

| 错误码   | 说明                                     | 建议                                                               |
| ----- | -------------------------------------- | ---------------------------------------------------------------- |
| -1    | 通用错误，msg里描述具体错误信息，比如API传参无效、网络失败等      | 根据msg内容调整API参数等                                                  |
| -2    | SDK not initialized                    | 当前SDK没有初始化完成，调用前可通过PSSDK.hasInitialized()判断或者放在SDK初始化完成回调里进行数据请求 |
| -3    | Not available for current user         | “合规设置”没有通过                                                       |
| 10001 | System error                           | 服务端异常，建议重试                                                       |
| 10003 | Invalid AppID                          | 检查AppID和securityKey是否正确                                          |
| 10004 | Signature Verification Failed          | 手机系统时间不准导致的报错                                                    |
| 10005 | Invalid Video Category                 | 类目ID参数错误                                                         |
| 10006 | Invalid ShortPlay ID                   | 短剧ID参数错误                                                         |
| 10007 | Wrong Episodes Index Number            | 剧集索引参数错误                                                         |
| 10008 | Wrong Request Parameter                | 参数存在无效值或必传参数为空                                                   |
| 10011 | Currently Not Available In Your Region | 当前不是合规地区，无法播放，建议：                                                |
| 10012 | Hit Frequency Limit                    | 调用数据获取API过于频繁                                                    |
| 10013 | No Valid Data Was Queried              | 未命中有效数据                                                          |

视频播放失败错误码参考：https://docs.byteplus.com/en/byteplus-vod/docs/error-code



# 高级功能

> 本节涉及功能和API均为可选使用

## 剧集收藏&#x20;

现有短剧收藏会根据初始化时传入的userID进行关联，如无userID我们会基于设备信息生成一个用户ID，并且生成的ID不会因为用户卸载重装而进行重置（但是如果用户恢复出厂设置、重置广告ID则会生成全新的用户ID）。

目前短剧没有获取收藏列表的API，但是您可通过请求短剧列表，获取shortPlay对象中的isCollected来判断用户是否收藏该剧集，用户可通过点击播放界面中的收藏按钮来控制收藏行为，同时在1700版本我们还增加了手动设置收藏状态的API。

* 设置收藏(1.7.0.0支持)

```plain&#x20;text
PSSDK.setCollected(shortPlayId,true,listner);
```



## 设置播放器填充模式

可以在创建播放页时，通过`builder.setVideoDisplayMode`配置视频画面显示模式，支持模式见 <https://docs.byteplus.com/en/docs/byteplus-vod/docs-implementing-basic-features-android#set-display-mode>，但是需要注意该设置可能会导致视频裁切



## 播放页底部插入Banner广告（1014版本支持）

创建播放页时，配置底部显示扩展区域，该区域固定值为50dp，不可进行动态调整。**此功能默认开启，可手动配置关闭**

```sql
builder.displayBottomExtraView(true);
```

在加载到banner广告后，将广告View设置到播放页上

```sql
View bottomView = getBottomAdView();
detailFragment.setBottomExtraViewContent(bottomView, PSSDK.BottomViewType.AD);


public enum BottomViewType {
    AD,
    CUSTOM
}
```

如果同时在剧集中插入了广告，希望banner广告不要同时显示，可以通过onItemSelected监听到显示广告内容时设置底部显示其他内容，参考demo里实现；

## 剧集中插播广告(1013版本支持)

在创建播放页时，实现getDetailDrawAdPositions、onObtainAdView等方法可实现在剧集之间插入广告View的效果，使用注意：

* 尽管提供了onPrepareAd回调供请求广告，建议在创建播放页之前或更早就预请求广告，保证onObtainAdView调用时有广告View可用

* 该接口不支持动态设置，且不支持在最后一集后面中插广告

```typescript
PSSDK.DetailPageConfig.Builder builder = new PSSDK.DetailPageConfig.Builder();
// 配置广告策略
builder.adCustomProvider(new PSSDK.AdCustomProvider() {
            @Override
            public List<Integer> getDetailDrawAdPositions() {
                ArrayList<Integer> integers = new ArrayList<>();
                // 在第1集、第3集、第50集后面插入广告
                integers.add(1);
                integers.add(3);
                integers.add(50);
                return integers;
            }

            @Override
            public PSSDK.DrawAdProvider getDrawAdProvider() {
                return new PSSDK.DrawAdProvider() {
                    @Override
                    public void onPrepareAd() {
                        // 快划到广告插入位置时调用，可以在这里请求广告
                        loadFeedAd();
                    }

                    @Override
                    public View onObtainAdView(int position, int index) {
                        // 返回广告View，如没有可用广告则返回null，即将划到广告插入位置时调用此方法
                        return createFeedAdView();
                    }

                    @Override
                    public void onDestroy() {
                        // 播放页退出时调用，可在这里释放广告资源
                    }
                };
            }
        })
```

## 搜索短剧（1013版本支持）

使用`PSSDK.searchDrama`可指定搜索关键词进行搜索

v1.1.0.0以下

```typescript
/**
 * 搜索短剧
 *
 * @param keyword   关键词，不能为空
 * @param isFuzzy   是否模糊搜索
 * @param pageIndex 分页索引，从1开始
 * @param pageCount 分页大小
 */
public static void searchDrama(String keyword, boolean isFuzzy, int pageIndex, int pageCount, DataApi.DataListRequestListener<ShortPlay> listener) {
```

v1.1.0.0以上

```typescript
/**
 * 搜索短剧
 *
 * @param keyword   关键词，不能为空
 * @param isFuzzy   是否模糊搜索
 * @param pageIndex 分页索引，从1开始
 * @param pageCount 分页大小
 */
public static void searchDrama(String keyword, boolean isFuzzy, int pageIndex, int pageCount, FeedListResultListener listener) {
```

## 手动预加载（1100版本支持）

**SDK在一般播放场景下，会根据播放情况自动预加载下一集**，当时在开发者自己实现滑滑流时（playSingleItem=true），由于SDK无法感知列表滑动情况和列表里其他项目，所以无法进行下一部剧的自动预加载；

为了兼容这种场景，在播放页Fragment上对外增加了手动预加载的API，开发者可根据列表滑动情况来调用下一部剧对应fragment的预加载API。

该API无法指定预加载哪一集，固定去预加载创建播放页时startPlayIndex指定的那一集。另外只创建playFragment调用该方法而不进行播放，仍然可以实现剧集的预加载。

```sql
playFragment.preLoadVideo(PSSDK.ActionResultListener resultListener)
```

## 播放器界面添加点赞收藏等视图组件(1100版本支持)

> 目前暂不支持自定义播放/暂停的按钮

对于点赞、收藏交互，我们会记录状态并同步到服务器，一般情况下开发者可以只关心View组件基于状态更新即可。

重写ShortPlayDetailPageListener.onObtainPlayerControlViews方法，返回符合条件的View对象集合，SDK会监听View的点击事件，来实现“用户点击切换-数据同步服务器-通知View更新”的流程

收藏、点赞的数据只跟开发者的单个App进行绑定，不与大盘数据共享。

点赞、收藏需要实现IControlStatusView接口

```sql
public interface IControlStatusView extends IControlView {
    /**
     * 设置最新状态到View上，在这里根据状态更新View，调用时机为
     *
     * @param shortPlay 当前观看的短剧
     * @param index     当前观看的第几集
     * @param status    当前选中状态
     * @param extraInfo 额外信息，比如点赞数、收藏数等
     */
    void setCurrentStatus(ShortPlay shortPlay, int index, ControlStatus status, StatusExtraInfo extraInfo);

    /**
     * 返回当前选中状态，一般情况下记录setCurrentStatus传过来的值，这里返回即可
     *
     * @param shortPlay 当前播放的短剧
     * @param index     当前播放的第几集
     * @return 当前选中状态
     */
    ControlStatus getCurrentStatus(ShortPlay shortPlay, int index);

    /**
     * View发生点击时回调此方法，开发者里这里返回新的状态，一般情况下返回currentStatus的取反
     *
     * @param shortPlay     当前观看的短剧
     * @param index         当前是第几集
     * @param currentStatus 当前选中状态
     * @return 新状态
     */
    ControlStatus onClicked(ShortPlay shortPlay, int index, ControlStatus currentStatus);
}
```

完整实现示例代码可参考Demo项目里的DramaPlayActivity

添加错误提示视图

```java
// 自定义错误视图UI组件
private static class CustomErrorView extends TextView implements PSSDK.IControlView {

    public CustomErrorView(Context context) {
        super(context);
        setText("Demo自定义错误页");
        setGravity(Gravity.CENTER);
        setBackgroundColor(Color.WHITE);
    }

    @Override
    public PSSDK.ControlViewType getControlViewType() {
        return PSSDK.ControlViewType.ERROR_PAGE;
    }
}


// 实现PSSDK.ShortPlayDetailPageListener#onObtainPlayerControlViews方法，在返回views里加上该错误视图
CustomErrorView errorView = new CustomErrorView(getApplicationContext());
errorView.setOnClickListener(new View.OnClickListener() {
    @Override
    public void onClick(View view) {
        // 点击此失败界面时，重新尝试播放
        detailFragment.startPlay();
    }
});
views.add(errorView);
```

## 自定义Loading提示（1300版本支持）

重写onObtainPlayerControlViews方法，返回自定义的loading视图对象

```java
@Override
public List<View> onObtainPlayerControlViews() {
    ArrayList<View> views = new ArrayList<>();

    // Loading
    CustomLoadingView loadingView = new CustomLoadingView(getApplicationContext());
    FrameLayout.LayoutParams loadingLP = new FrameLayout.LayoutParams(DemoUtils.dp2Px(getApplicationContext(), 48), DemoUtils.dp2Px(getApplicationContext(), 48));
    loadingLP.gravity = Gravity.CENTER;
    loadingView.setLayoutParams(loadingLP);
    views.add(loadingView);

    return views;
}
```

自定义Loading视图代码示例

```java
private static class CustomLoadingView extends ProgressBar implements PSSDK.IControlLoadingView {

    public CustomLoadingView(Context context) {
        super(context);
    }

    @Override
    public PSSDK.ControlViewType getControlViewType() {
        return PSSDK.ControlViewType.Loading;
    }

    @Override
    public void bindItemData(ShortPlayFragment shortPlayFragment, ShortPlay shortPlay, int index) {

    }

    @Override
    public void startAnimating() {
        // 回调时机：显示loading时
    }

    @Override
    public void stopAnimating() {
        // 回调时机，隐藏loading时
    }
}
```

## 自定义播放进度条（1300版本支持）

首先隐藏默认的进度条

```java
builder.displayProgressBar(false);
```

监听进度回调，更新自定义的进度条

```java
public void onProgressChange(ShortPlay shortPlay, int index, int currentPlayTimeInSeconds, int durationInSeconds) {
// currentPlayTimeInSeconds: 当前播放进度，秒
// durationInSeconds: 视频时长，秒
}
```

完整实现示例代码可参考Demo项目里的DramaPlayActivity

## 播放进度记录和恢复（1104版本支持）

场景：在滑滑流或者播放历史界面等情况进入播放页时，希望恢复到之前的进度，比如用户在外面观看第2集看到35秒，点击进入播放页时可指定从第2集的35秒开始播放

1. 记录进度

播放页回调onProgressChange方法会定时回调同步视频播放进度，单位秒，可在此记录播放情况

currentPlayTimeInSeconds: 当前播放进度，单位秒

durationInSeconds: 视频总时长，单位秒

```java
public void onProgressChange（ShortPlay shortPlay, int index, long currentPlayTimeInSeconds, long durationInSeconds）
```

* 创建播放页时指定进度

通过播放页builder指定进入播放页首个视频的播放进度，单位秒

```java
builder.startPlayIndex(2)；// 从第2集开始播
builder.startPlayAtTimeSeconds(5)；// 首个视频（结合上一行配置即第2集）从第5秒开始播

```

* 实时调整进度

```java
// 调整当前视频播放进度到第30秒
playFragment.setCurrentPlayTimeSeconds(30)

// 播放第10集，从第5秒开始播
playFragment.startPlayIndexAndTimeSeconds(10,5)
```

## 切换分辨率

视频一般支持多种分辨率，SDK会根据设备以及网络情况来调整分辨率，开发者也可根据自己的需求来自行设置分辨率，设置一次全局生效。目前大部分剧集都只支持到720P。

每集开始播放时，会通过`onVideoInfoFetched`回调告知视频信息，可以拿到该集支持的分辨率列表，这里可以通过让用户选择或者开发者逻辑来确定想播放的分辨率，通过`detailFragment.setResolution(resolution)`来切换分辨率

```java
public void onVideoInfoFetched(ShortPlay shortPlay, int index, PSSDK.VideoPlayInfo videoPlayInfo) {
    // 每一集视频准备好时调用此方法告知本集的视频信息
    Resolution[] resolutions = videoPlayInfo.supportResolutions;
    
}
```

分辨率范围

```java
public enum Resolution {
    Standard("360p", "medium"),
    High("480p", "higher"),
    SuperHigh("720p", "highest"),
    ExtremelyHigh("1080p", "original")
}
```

## 沉浸式模式

沉浸式：播放界面文字隐藏

SDK默认不开启沉浸式模式，可通过配置`enableImmersiveMode(XX)`来开启并指定用户无操作XXms后进入沉浸式模式，在开启后，可通过`onEnterImmersiveMode`和`onExitImmersiveMode`来感知模式切换



## 倍速播放（1300版本支持）

播放页Fragment提供设置倍速播放的API，倍速必须大于0，设置一次后，对该播放页里所有视频播放都有效，最大支持3倍速

```java
playFragment.setVideoSpeed(2.0f);
```



## 自动播放下一集

SDK默认每集循环播放，需要需要自动播放下一集，可在创建播放页时配置

```java
PSSDK.DetailPageConfig.Builder builder = new PSSDK.DetailPageConfig.Builder();
// 开启自动播放下一集
builder.enableAutoPlayNext(true);
```

## 计算缓存

SDK不提供缓存大小的接口，但是开发者可以通过计算缓存目录`cache/pssdk`的大小来间接实现。

缓存大小最大1G。如需手动清除请调用清除缓存的接口。



## 清除缓存

缓存包括视频缓存，清除后已观看过的视频再次观看时需要重新下载缓存。

```java
/**
 * 清除本地缓存
 */
PSSDK.clearLocalCache()；
```

## 静音

> 1400支持

播放页Fragment支持修改静音状态，对fragment内所有视频有效

```java
playFragment.setMuted(true);
```

## 播放页默认封面设置

> 1400支持

创建播放页时支持配置默认封面图，播放页请求封面URL时显示该默认图

```java
builder.setDefaultCoverImage(Drawable defaultCoverDrawable)
```

## 下拉刷新功能

目前SDK没有下拉刷新的接口，但是开发者可通过自行打乱短剧ID的方式来间接实现乱序的功能。具体接口可参考“[使用指定短剧ID获取短剧对象](https://bytedance.sg.larkoffice.com/docx/G3m4dl0ELoLiT1xRkRPlcRrrgId#share-YXMid7WD1oXEgjxF3EzlBZ1lgYe)”



# 常见问题

## SDK适用国家或地区

目前SDK只对以下地区可用，其他地区请求均会提示失败"Currently not available in your region"

```java
中国台湾、泰国、越南、马来西亚、印度尼西亚、菲律宾、新加坡、柬埔寨、日本、韩国、巴西、澳大利亚、新西兰、阿根廷、智利、哥伦比亚、厄瓜多尔、秘鲁、乌拉圭、墨西哥、沙特阿拉伯、阿联酋、土耳其、哈萨克斯坦、巴基斯坦、科威特、伊拉克、卡塔尔、约旦、阿曼、巴林、黎巴嫩、斯里兰卡、阿塞拜疆、白俄罗斯、乌克兰、埃及、南非共和国、摩洛哥、肯尼亚、阿尔及利亚。
```

## 类目名称和ID的对应关系

获取列表时，可指定类目ID来获取(类目名称的其他语言翻译后续会补充)

| 类目ID | 类目名称（中文）                 |
| ---- | ------------------------ |
| 1    | 男频/Male Category         |
| 2    | 女频/Female Category       |
| 4    | 悬疑/Suspense              |
| 5    | 现言/Contemporary Romance  |
| 6    | 古言/Historical Romance    |
| 7    | 军事/Military              |
| 8    | 玄幻/Fantasy               |
| 11   | 喜剧/Comedy                |
| 12   | 动作/Action                |
| 13   | 二次元/Anime                |
| 14   | 其他/Others                |
| 1701 | 逆袭/Comeback              |
| 1702 | 重生/Reborn                |
| 1704 | 复仇/Revenge               |
| 1706 | 霸总/Domineering President |
| 1709 | 战神/God Of War            |
| 1751 | 萌宝/Cute Baby             |
| 1851 | 团宠/Group Favorite        |
| 1901 | 甜宠/Sweet Love            |
| 1902 | 系统/System                |
| 1951 | 虐恋/Sadistic Love         |
| 1952 | 年代/Era                   |
| 1953 | 亲情/Family                |
| 1954 | 体育/Sports                |

## 查看SDK测试日志

1. 初始化SDK时配置`debug(true)`

2. 日志TAG是"`PSSDK`"

## 如何使用播放器播放自有剧

我们支持开发者使用Byteplus的播放器播放自己的剧，如需了解更多或提供技术支持，请联系Byteplus团队。

