# TikTok风格短剧Feed实现

## 概述

本项目成功实现了类似TikTok主页的视频Feed列表，具有垂直滑动和snap效果。实现基于Jetpack Compose UI + ViewPager2 + FragmentStateAdapter架构，使用Dramaverse SDK的原生Fragment进行视频播放，确保最佳的播放体验和性能。

## 主要功能

### 1. 垂直视频Feed
- 使用`VerticalPager`实现垂直滑动
- 每个页面显示一个短剧视频
- 支持snap-to-item效果，确保每次滑动都精确停在一个视频上

### 2. 原生视频播放
- 使用Dramaverse SDK的原生ShortPlayFragment进行播放
- 每个页面都是一个完整的播放器Fragment
- 自动播放和生命周期管理
- 支持预加载下一个视频

### 3. 交互功能
- 点赞功能（UI已实现，逻辑待完善）
- 分享功能（UI已实现，逻辑待完善）
- 播放控制
- 返回导航

### 4. 数据管理
- 使用MVI架构模式
- 支持分页加载
- 下拉刷新功能
- 错误处理和重试机制

## 技术架构

### 核心组件

1. **ShortsScreen** - 主要的Compose屏幕
   - 使用AndroidView集成ViewPager2
   - 管理数据加载和错误状态
   - 处理分页加载逻辑

2. **ShortsViewPagerAdapter** - ViewPager2适配器
   - 继承FragmentStateAdapter
   - 为每个短剧创建ShortPlayFragment
   - 管理Fragment生命周期和预加载

3. **CustomActionButton** - 自定义操作按钮
   - 圆形半透明背景
   - 点击动画效果
   - 用于点赞、分享、收藏等操作

4. **ShortsOverlayView** - 视频信息覆盖层
   - 实现PSSDK.IControlView接口
   - 显示视频标题、描述、集数信息
   - 底部信息展示

4. **ShortsUiModel** - 状态管理
   - 使用Orbit MVI框架
   - 管理短剧数据加载
   - 处理分页和刷新逻辑

5. **ShortsUiState** - UI状态定义
   - 短剧列表数据
   - 加载状态
   - 错误状态

### SDK集成

- **Dramaverse SDK**: 提供短剧内容和播放功能
- **初始化**: 在AppInitializer中完成SDK初始化
- **配置**: 支持调试模式和合规设置

## 实现细节

### ViewPager2集成和Snap效果

```kotlin
AndroidView(
  factory = { context ->
    ViewPager2(context).apply {
      orientation = ViewPager2.ORIENTATION_VERTICAL
      offscreenPageLimit = 1
    }
  },
  modifier = modifier.fillMaxSize()
) { viewPager ->
  val adapter = ShortsViewPagerAdapter(activity, uiState.shortPlays) { position ->
    // 预加载和分页逻辑
    if (position >= uiState.shortPlays.size - 3 && uiState.hasMore && !uiState.isLoading) {
      uiModel.loadMore()
    }
  }
  viewPager.adapter = adapter
}
```

### 预加载机制

```kotlin
LaunchedEffect(pagerState.currentPage) {
  val currentPage = pagerState.currentPage
  val totalPages = uiState.shortPlays.size
  
  // 当滑动到倒数第3个时开始加载更多
  if (currentPage >= totalPages - 3 && uiState.hasMore && !uiState.isLoading) {
    onLoadMore()
  }
}
```

### ShortPlayFragment创建和配置

使用PSSDK.createDetailFragment创建原生播放器Fragment：

```kotlin
val builder = PSSDK.DetailPageConfig.Builder()
builder
  .hideLeftTopCloseAndTitle(true, null) // 隐藏顶部标题栏
  .displayBottomExtraView(false) // 不显示底部额外视图
  .displayProgressBar(false) // 隐藏进度条，因为是Feed模式
  .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_TITLE, false) // 隐藏底部标题
  .displayTextVisibility(PSSDK.DetailPageConfig.TEXT_POS_BOTTOM_DESC, false) // 隐藏底部描述
  .playSingleItem(true) // 只播放单个视频，不允许滑动切换
  .enableImmersiveMode(0) // 禁用沉浸式模式

val detailFragment = PSSDK.createDetailFragment(shortPlay, builder.build(), listener)
```

### 自定义控制视图

通过onObtainPlayerControlViews()添加自定义UI元素：

```kotlin
override fun onObtainPlayerControlViews(): List<View>? {
  val views = ArrayList<View>()

  // 添加分享按钮
  val shareView = createShareButton(shortPlay)
  views.add(shareView)

  // 添加点赞按钮
  val likeView = createLikeButton(shortPlay)
  views.add(likeView)

  // 添加信息覆盖层
  val overlayView = createOverlayView(shortPlay)
  views.add(overlayView)

  return views
}
```

## 文件结构

```
app/src/main/java/com/example/shorts/ui/node/screen/home/<USER>/
├── ShortsScreen.kt                    # 主屏幕（Compose + AndroidView）
├── components/
│   ├── ShortsViewPagerAdapter.kt     # ViewPager2适配器
│   ├── CustomActionButton.kt         # 自定义操作按钮
│   ├── ShortsOverlayView.kt          # 视频信息覆盖层
│   ├── VideoFeedItem.kt              # 视频Feed项（备用）
│   ├── InlineVideoPlayer.kt          # 内嵌视频播放器（备用）
│   └── VideoPlayerScreen.kt          # 全屏视频播放器（备用）
└── model/
    ├── ShortsUiState.kt              # UI状态
    └── ShortsUiModel.kt              # 状态管理
```

## 使用方法

1. 确保Dramaverse SDK已正确配置
2. 在HomeNode中，Shorts tab会显示视频Feed
3. 用户可以垂直滑动浏览不同的短剧
4. 每个页面都是一个完整的ShortPlayFragment，自动播放
5. 支持点赞、分享、收藏等交互功能
6. 底部显示视频标题、描述等信息
7. 自动预加载下一个视频，提升用户体验

## 待完善功能

1. **点赞功能**: 需要实现与后端的交互
2. **分享功能**: 需要实现分享逻辑
3. **收藏功能**: 集成SDK的收藏API
4. **播放历史**: 记录用户观看进度
5. **个性化推荐**: 基于用户行为的内容推荐

## 注意事项

1. **地区限制**: Dramaverse SDK只在特定地区可用
2. **网络要求**: 需要稳定的网络连接来加载视频内容
3. **性能优化**: 大量视频数据需要考虑内存管理
4. **用户体验**: 需要处理加载状态和错误情况

## 测试建议

1. 测试垂直滑动的流畅性
2. 验证snap效果是否正确
3. 测试视频播放功能
4. 验证分页加载机制
5. 测试错误处理和重试功能
